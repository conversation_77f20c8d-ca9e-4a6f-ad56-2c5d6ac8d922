<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Template Name -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="模板名称">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/preset_name_edit_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Template Content -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="模板内容">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/preset_template_edit_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="monospace"
            android:inputType="textMultiLine"
            android:maxLines="5"
            android:minLines="3"
            android:scrollbars="vertical" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Template Description -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="模板描述（可选）">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/preset_description_edit_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textMultiLine"
            android:maxLines="3"
            android:minLines="2" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Help Text -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="提示：使用 {变量名} 格式插入变量，如 {voice_text}、{date}、{time} 等"
        android:textColor="@android:color/darker_gray"
        android:textSize="12sp" />

</LinearLayout>
