<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <SwitchPreferenceCompat
        android:key="startonbootup"
        android:summary="@string/startonbootup_summary"
        android:title="@string/startonbootup_title"
        app:iconSpaceReserved="false" />

    <SwitchPreferenceCompat
        android:key="startonapplaunch"
        android:summary="@string/startonapplaunch_summary"
        android:title="@string/startonapplaunch_title"
        app:iconSpaceReserved="false" />



    <SwitchPreferenceCompat
        android:key="haptic_feedback_enabled"
        android:defaultValue="true"
        android:summary="@string/haptic_feedback_summary"
        android:title="@string/haptic_feedback_title"
        app:iconSpaceReserved="false" />

    <ListPreference
        android:key="haptic_feedback_intensity"
        android:title="@string/haptic_feedback_intensity_title"
        android:summary="@string/haptic_feedback_intensity_summary"
        android:entries="@array/haptic_feedback_intensity_entries"
        android:entryValues="@array/haptic_feedback_intensity_values"
        android:defaultValue="MEDIUM"
        android:dependency="haptic_feedback_enabled"
        app:iconSpaceReserved="false" />

    <ListPreference
        android:key="coordinatedisplayformat"
        android:title="@string/coordinate_display_format"
        app:iconSpaceReserved="false" />

    <SwitchPreferenceCompat
        android:key="hide_notification_buttons"
        android:summary="@string/restart_required"
        android:title="@string/hide_notification_buttons"
        app:iconSpaceReserved="false" />

    <SwitchPreferenceCompat
        android:key="hide_notification_from_lock_screen"
        android:title="@string/hide_notification_from_lock_screen"
        android:summary="@string/restart_required"
        android:defaultValue="true"
        app:iconSpaceReserved="false"/>`



    <SeekBarPreference
        android:key="annotations_button_count"
        android:title="@string/annotation_button_count_title"
        android:summary="@string/annotation_button_count_summary"
        android:defaultValue="9"
        android:min="9"
        android:max="25"
        app:iconSpaceReserved="false" />

    <PreferenceCategory
        android:title="@string/annotation_layout_category"
        app:iconSpaceReserved="false">



        <ListPreference
            android:key="annotation_view_mode"
            android:title="@string/annotation_view_mode_title"
            android:summary="@string/annotation_view_mode_summary"
            android:entries="@array/annotation_view_mode_entries"
            android:entryValues="@array/annotation_view_mode_values"
            android:defaultValue="grid"
            app:iconSpaceReserved="false" />

        <!-- Voice input settings -->
        <SwitchPreferenceCompat
            android:key="voice_input_enabled"
            android:title="@string/pref_voice_input_enabled_title"
            android:summary="@string/pref_voice_input_enabled_summary"
            android:defaultValue="false"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="voice_input_language"
            android:title="@string/pref_voice_input_language_title"
            android:summary="@string/pref_voice_input_language_summary"
            android:dependency="voice_input_enabled"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="voice_input_timeout"
            android:title="@string/pref_voice_input_timeout_title"
            android:summary="@string/pref_voice_input_timeout_summary"
            android:dependency="voice_input_enabled"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <!-- Audio Recording Settings Category -->
    <PreferenceCategory
        android:title="@string/audio_recording_category"
        app:iconSpaceReserved="false">

        <Preference
            android:key="audio_recording_silence_timeout"
            android:title="@string/audio_recording_silence_timeout_title"
            android:summary="@string/audio_recording_silence_timeout_summary"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="audio_recording_silence_threshold"
            android:title="@string/audio_recording_silence_threshold_title"
            android:summary="@string/audio_recording_silence_threshold_summary"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:key="audio_recording_advanced_detection"
            android:title="@string/audio_recording_advanced_detection_title"
            android:summary="@string/audio_recording_advanced_detection_summary"
            android:defaultValue="true"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <PreferenceCategory
        android:title="@string/audio_feedback_category"
        app:iconSpaceReserved="false">

        <!-- Voice Input Button Audio Settings -->
        <ListPreference
            android:key="voice_input_button_audio_type"
            android:title="@string/voice_input_button_audio_title"
            android:summary="@string/voice_input_button_audio_summary"
            android:entries="@array/audio_feedback_type_entries"
            android:entryValues="@array/audio_feedback_type_values"
            android:defaultValue="none"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="voice_input_button_custom_audio_path"
            android:title="@string/custom_audio_file_title"
            android:summary="@string/custom_audio_file_summary"
            android:dependency="voice_input_button_audio_type"
            app:iconSpaceReserved="false" />

        <!-- Text Input Button Audio Settings -->
        <ListPreference
            android:key="text_input_button_audio_type"
            android:title="@string/text_input_button_audio_title"
            android:summary="@string/text_input_button_audio_summary"
            android:entries="@array/audio_feedback_type_entries"
            android:entryValues="@array/audio_feedback_type_values"
            android:defaultValue="none"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="text_input_button_custom_audio_path"
            android:title="@string/custom_audio_file_title"
            android:summary="@string/custom_audio_file_summary"
            android:dependency="text_input_button_audio_type"
            app:iconSpaceReserved="false" />

        <!-- Counter Button Audio Settings -->
        <ListPreference
            android:key="counter_button_audio_type"
            android:title="@string/counter_button_audio_title"
            android:summary="@string/counter_button_audio_summary"
            android:entries="@array/audio_feedback_type_entries"
            android:entryValues="@array/audio_feedback_type_values"
            android:defaultValue="none"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="counter_button_custom_audio_path"
            android:title="@string/custom_audio_file_title"
            android:summary="@string/custom_audio_file_summary"
            android:dependency="counter_button_audio_type"
            app:iconSpaceReserved="false" />

        <!-- Single Click Button Audio Settings -->
        <ListPreference
            android:key="single_click_button_audio_type"
            android:title="@string/single_click_button_audio_title"
            android:summary="@string/single_click_button_audio_summary"
            android:entries="@array/audio_feedback_type_entries"
            android:entryValues="@array/audio_feedback_type_values"
            android:defaultValue="none"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="single_click_button_custom_audio_path"
            android:title="@string/custom_audio_file_title"
            android:summary="@string/custom_audio_file_summary"
            android:dependency="single_click_button_audio_type"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <Preference
        android:defaultValue="false"
        android:key="enableDisableGps"
        android:summary="@string/enabledisablegps_summary"
        android:title="@string/enabledisablegps_title"
        app:iconSpaceReserved="false" />



    <SwitchPreferenceCompat
        android:defaultValue="false"
        android:key="debugtofile"
        android:summary="@string/debuglog_summary"
        android:title="@string/debuglog_title"
        app:iconSpaceReserved="false" />

    <Preference
        android:enabled="true"
        android:key="debuglogtoemail"
        android:summary="@string/debuglog_attach_to_email_summary"
        android:title="@string/debuglog_attach_to_email"
        app:iconSpaceReserved="false" />

    <!--https://www.flaticon.com/free-icon/dark-mode_5262027-->
    <ListPreference
        android:defaultValue="system"
        android:entries="@array/app_theme_options"
        android:entryValues="@array/app_theme_values"
        android:icon="@drawable/dark_mode"
        android:key="app_theme_setting"
        android:summary="@string/app_theme_setting"
        android:title="@string/app_theme_title"
        app:iconSpaceReserved="false" />

    <!--https://www.flaticon.com/free-icon/translate_889588-->
    <ListPreference
        android:icon="@drawable/translate"
        android:key="changelanguage"
        android:summary="@string/change_language_summary"
        android:title="@string/change_language_title"
        app:iconSpaceReserved="false" />

    <!--https://www.flaticon.com/free-icon/close_463612-->
    <Preference
        android:enabled="true"
        android:icon="@drawable/reset"
        android:key="resetapp"
        android:summary="@string/reset_app_summary"
        android:title="@string/reset_app_title"
        app:iconSpaceReserved="false" />

    <Preference
        android:key="about_version_info"
        app:iconSpaceReserved="false" />



</PreferenceScreen>
