/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.view.KeyEvent;

import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.ui.components.external.ExternalControlManager;

import org.slf4j.Logger;

/**
 * BroadcastReceiver to handle media button events from headsets and other external devices
 */
public class MediaButtonReceiver extends BroadcastReceiver {
    private static final Logger LOG = Logs.of(MediaButtonReceiver.class);

    @Override
    public void onReceive(Context context, Intent intent) {
        LOG.debug("MediaButtonReceiver.onReceive called with action: {}", intent.getAction());

        if (Intent.ACTION_MEDIA_BUTTON.equals(intent.getAction())) {
            KeyEvent keyEvent = intent.getParcelableExtra(Intent.EXTRA_KEY_EVENT);
            if (keyEvent != null) {
                LOG.info("Media button event received: keyCode={} ({}), action={}",
                         keyEvent.getKeyCode(), KeyEvent.keyCodeToString(keyEvent.getKeyCode()), keyEvent.getAction());

                // Try to get the ExternalControlManager from the running service
                try {
                    // Since we can't easily get the ExternalControlManager from a static context,
                    // we'll send a broadcast to the main activity to handle this
                    Intent mediaButtonIntent = new Intent("com.mendhak.gpslogger.MEDIA_BUTTON_EVENT");
                    mediaButtonIntent.putExtra(Intent.EXTRA_KEY_EVENT, keyEvent);
                    mediaButtonIntent.setPackage(context.getPackageName());
                    context.sendBroadcast(mediaButtonIntent);

                    LOG.info("Forwarded media button event to main activity: keyCode={}", keyEvent.getKeyCode());
                } catch (Exception e) {
                    LOG.error("Error handling media button event", e);
                }
            } else {
                LOG.warn("Media button event received but KeyEvent is null");
            }
        } else {
            LOG.debug("Received non-media button action: {}", intent.getAction());
        }
    }
}
