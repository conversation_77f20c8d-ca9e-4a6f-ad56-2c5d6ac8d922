/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.dialogs;

import android.app.Dialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;

import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.ui.components.template.AnnotationTemplateEngine;
import com.mendhak.gpslogger.ui.components.template.TemplateParser;

import org.slf4j.Logger;

/**
 * Dialog for editing template presets
 */
public class EditTemplatePresetDialog extends DialogFragment {
    
    private static final Logger LOG = Logs.of(EditTemplatePresetDialog.class);
    
    private EditText nameEditText;
    private EditText templateEditText;
    private EditText descriptionEditText;
    
    private String originalName;
    private String originalTemplate;
    private String originalDescription;
    
    private OnPresetEditedListener listener;
    
    public interface OnPresetEditedListener {
        void onPresetEdited(String name, String template, String description);
    }
    
    public void setOnPresetEditedListener(OnPresetEditedListener listener) {
        this.listener = listener;
    }
    
    public void setPreset(String name, String template, String description) {
        this.originalName = name;
        this.originalTemplate = template;
        this.originalDescription = description;
    }
    
    // Convenience method for TemplatePreset object
    public void setPreset(Object preset) {
        try {
            // Use reflection to access fields since TemplatePreset is private
            java.lang.reflect.Field nameField = preset.getClass().getDeclaredField("name");
            java.lang.reflect.Field templateField = preset.getClass().getDeclaredField("template");
            java.lang.reflect.Field descriptionField = preset.getClass().getDeclaredField("description");
            
            nameField.setAccessible(true);
            templateField.setAccessible(true);
            descriptionField.setAccessible(true);
            
            this.originalName = (String) nameField.get(preset);
            this.originalTemplate = (String) templateField.get(preset);
            this.originalDescription = (String) descriptionField.get(preset);
        } catch (Exception e) {
            LOG.error("Error accessing preset fields", e);
        }
    }
    
    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.dialog_edit_template_preset, null);

        nameEditText = view.findViewById(R.id.preset_name_edit_text);
        templateEditText = view.findViewById(R.id.preset_template_edit_text);
        descriptionEditText = view.findViewById(R.id.preset_description_edit_text);

        // Set original values
        if (originalName != null) nameEditText.setText(originalName);
        if (originalTemplate != null) templateEditText.setText(originalTemplate);
        if (originalDescription != null) descriptionEditText.setText(originalDescription);

        AlertDialog dialog = new AlertDialog.Builder(requireContext())
                .setTitle("编辑模板")
                .setView(view)
                .setPositiveButton("保存", (d, which) -> savePreset())
                .setNegativeButton("取消", null)
                .setNeutralButton("验证", null) // Set to null, we'll handle it manually
                .create();

        // Override the neutral button to prevent dialog dismissal
        dialog.setOnShowListener(dialogInterface -> {
            android.widget.Button validateButton = dialog.getButton(AlertDialog.BUTTON_NEUTRAL);
            validateButton.setOnClickListener(v -> {
                validateTemplate();
                // Don't dismiss the dialog
            });
        });

        return dialog;
    }
    
    private void savePreset() {
        String name = nameEditText.getText().toString().trim();
        String template = templateEditText.getText().toString().trim();
        String description = descriptionEditText.getText().toString().trim();
        
        // Validate input
        if (TextUtils.isEmpty(name)) {
            Toast.makeText(getContext(), "请输入模板名称", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (TextUtils.isEmpty(template)) {
            Toast.makeText(getContext(), "请输入模板内容", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // Validate template syntax
        AnnotationTemplateEngine templateEngine = AnnotationTemplateEngine.getInstance();
        TemplateParser.ValidationResult validation = templateEngine.validateTemplate(template);
        if (!validation.isValid()) {
            Toast.makeText(getContext(), "模板语法错误: " + validation.getErrorMessage(), 
                          Toast.LENGTH_LONG).show();
            return;
        }
        
        // Call listener
        if (listener != null) {
            listener.onPresetEdited(name, template, description);
        }
        
        dismiss();
    }
    
    private void validateTemplate() {
        String template = templateEditText.getText().toString().trim();
        
        if (TextUtils.isEmpty(template)) {
            Toast.makeText(getContext(), "请输入模板内容", Toast.LENGTH_SHORT).show();
            return;
        }
        
        AnnotationTemplateEngine templateEngine = AnnotationTemplateEngine.getInstance();
        TemplateParser.ValidationResult validation = templateEngine.validateTemplate(template);
        
        if (validation.isValid()) {
            Toast.makeText(getContext(), "✓ 模板语法正确", Toast.LENGTH_SHORT).show();
        } else {
            Toast.makeText(getContext(), "✗ 模板语法错误: " + validation.getErrorMessage(), 
                          Toast.LENGTH_LONG).show();
        }
    }
}
