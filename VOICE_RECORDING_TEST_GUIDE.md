# GPSLogger 语音录音功能测试指南

## 🎯 测试目标

验证改进后的语音录音功能能够：
1. 准确检测语音结束并自动停止录音
2. 在不同环境下正常工作
3. 提供良好的用户体验
4. 在中国区手机上稳定运行

## 📱 安装测试

### 1. 安装APK
```bash
# APK位置
gpslogger/build/outputs/apk/debug/gpslogger-debug.apk

# 安装命令（如果使用ADB）
adb install gpslogger-debug.apk
```

### 2. 权限设置
- 启动应用后，授予录音权限
- 确保位置权限也已授予

## ⚙️ 功能配置测试

### 1. 访问录音设置
1. 打开GPSLogger应用
2. 点击左上角菜单 → 设置
3. 找到"录音设置"分类
4. 验证以下选项是否存在：
   - 静音超时时间
   - 静音检测阈值  
   - 智能语音检测

### 2. 配置选项测试
#### A. 静音超时时间
- 默认值：2秒
- 可调范围：1-5秒
- 测试：设置为1秒，验证录音是否在1秒静音后停止

#### B. 静音检测阈值
- 默认值：800
- 可调范围：200-2000
- 测试：
  - 设置为200（高敏感度）：轻声说话也能检测
  - 设置为2000（低敏感度）：需要较大声音才能检测

#### C. 智能语音检测
- 默认：启用
- 测试：关闭后应回到简单检测模式

## 🎤 录音功能测试

### 1. 基础录音测试
1. 进入Annotation View界面
2. 点击任意"注释 X"按钮
3. 说话："这是一个测试录音"
4. 停止说话，等待自动停止
5. 验证：录音应在2秒内自动停止

### 2. 环境适应性测试

#### A. 安静环境测试
- **环境**：室内安静环境
- **测试内容**：
  - 正常语速说话
  - 低声说话
  - 快速说话
  - 间断性说话（中间有停顿）
- **预期结果**：所有情况下都能准确检测语音结束

#### B. 背景噪音测试
- **环境**：有背景音乐或电视声音
- **测试内容**：正常说话
- **预期结果**：不受背景噪音影响，能准确检测人声结束

#### C. 户外环境测试
- **环境**：户外有风噪或交通噪音
- **测试内容**：清晰说话
- **预期结果**：能区分人声和环境噪音

### 3. 边界情况测试

#### A. 极短语音
- 说一个字或很短的词
- 验证：不会因为太短而被忽略

#### B. 长时间录音
- 连续说话超过30秒
- 验证：不会意外停止

#### C. 咳嗽/清嗓子
- 在说话过程中咳嗽或清嗓子
- 验证：不会误判为语音结束

## 📊 性能测试

### 1. 响应时间测试
- 测量从停止说话到录音停止的时间
- 不同配置下的响应时间对比
- 记录：
  - 1秒超时设置：实际停止时间
  - 2秒超时设置：实际停止时间
  - 3秒超时设置：实际停止时间

### 2. 电池消耗测试
- 连续使用录音功能30分钟
- 对比开启/关闭智能检测的电池消耗
- 记录CPU使用率变化

### 3. 内存使用测试
- 监控录音过程中的内存使用
- 验证是否存在内存泄漏

## 🔧 故障排除测试

### 1. 权限问题
- 拒绝录音权限后的错误提示
- 重新授权后的功能恢复

### 2. 硬件兼容性
- 不同品牌手机的麦克风兼容性
- 蓝牙耳机录音测试

### 3. 系统版本兼容性
- Android 8.0+ 各版本测试
- 中国区ROM适配测试

## 📝 测试记录表

### 基础功能测试
| 测试项目 | 预期结果 | 实际结果 | 通过/失败 | 备注 |
|---------|---------|---------|----------|------|
| 安静环境录音 | 2秒内停止 |  |  |  |
| 背景音乐环境 | 正常检测 |  |  |  |
| 户外噪音环境 | 正常检测 |  |  |  |
| 配置界面显示 | 显示所有选项 |  |  |  |
| 阈值调整 | 生效 |  |  |  |
| 超时调整 | 生效 |  |  |  |

### 性能测试
| 测试项目 | 配置 | 响应时间 | CPU使用率 | 内存使用 | 备注 |
|---------|------|---------|----------|----------|------|
| 智能检测开启 | 默认 |  |  |  |  |
| 智能检测关闭 | 简单模式 |  |  |  |  |
| 高敏感度 | 阈值200 |  |  |  |  |
| 低敏感度 | 阈值2000 |  |  |  |  |

## 🚨 已知问题和解决方案

### 问题1：录音不停止
**可能原因**：
- 环境噪音过大
- 阈值设置过低
- 麦克风硬件问题

**解决方案**：
- 调高静音检测阈值
- 关闭智能检测使用简单模式
- 检查麦克风权限

### 问题2：录音过早停止
**可能原因**：
- 阈值设置过高
- 超时时间过短
- 说话声音过小

**解决方案**：
- 调低静音检测阈值
- 增加超时时间
- 提高说话音量

### 问题3：配置不生效
**可能原因**：
- 应用需要重启
- 配置保存失败

**解决方案**：
- 重启应用
- 重新设置配置

## ✅ 测试完成标准

测试通过需要满足：
1. 所有基础功能测试通过
2. 至少在3种不同环境下测试通过
3. 配置选项全部正常工作
4. 性能指标在可接受范围内
5. 在至少2个不同品牌的中国区手机上测试通过

## 📞 问题反馈

如发现问题，请记录：
1. 手机型号和Android版本
2. 具体的测试环境
3. 复现步骤
4. 预期结果vs实际结果
5. 相关配置设置
6. 错误日志（如有）
