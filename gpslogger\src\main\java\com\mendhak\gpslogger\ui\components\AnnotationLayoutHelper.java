/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components;

import android.content.Context;
import android.util.DisplayMetrics;
import com.mendhak.gpslogger.ui.components.LayoutEnums.*;

/**
 * Helper class for calculating optimal layout for annotation buttons
 */
public class AnnotationLayoutHelper {

    /**
     * Layout configuration for annotation buttons
     */
    public static class LayoutConfig {
        public final int columns;
        public final int rows;
        public final int totalButtons;
        public final LayoutStyle layoutStyle;
        public final int spacingDp;
        public final ViewMode viewMode;

        public LayoutConfig(int columns, int rows, int totalButtons,
                           LayoutStyle layoutStyle, int spacingDp, ViewMode viewMode) {
            this.columns = columns;
            this.rows = rows;
            this.totalButtons = totalButtons;
            this.layoutStyle = layoutStyle;
            this.spacingDp = spacingDp;
            this.viewMode = viewMode;
        }

        // Backward compatibility constructor
        public LayoutConfig(int columns, int rows, int totalButtons) {
            this(columns, rows, totalButtons, LayoutStyle.RECTANGULAR, 4, ViewMode.GRID);
        }
    }

    /**
     * Calculate optimal layout configuration for given number of buttons
     * @param context Application context for screen metrics
     * @param buttonCount Number of buttons to display (9-25)
     * @return LayoutConfig with optimal columns and rows
     */
    public static LayoutConfig calculateOptimalLayout(Context context, int buttonCount) {
        return calculateOptimalLayout(context, buttonCount, LayoutStyle.RECTANGULAR,
                                     SpacingMode.STANDARD, ViewMode.GRID);
    }

    /**
     * Calculate optimal layout configuration with style options
     * @param context Application context for screen metrics
     * @param buttonCount Number of buttons to display (9-25)
     * @param layoutStyle Layout style (rectangular or circular)
     * @param spacingMode Spacing mode (compact, standard, loose)
     * @param viewMode View mode (grid or list)
     * @return LayoutConfig with optimal layout configuration
     */
    public static LayoutConfig calculateOptimalLayout(Context context, int buttonCount,
                                                     LayoutStyle layoutStyle,
                                                     SpacingMode spacingMode,
                                                     ViewMode viewMode) {
        return calculateOptimalLayout(context, buttonCount, layoutStyle, spacingMode, viewMode, -1);
    }

    /**
     * Calculate optimal layout configuration with custom spacing
     * @param context Application context for screen metrics
     * @param buttonCount Number of buttons to display (9-25)
     * @param layoutStyle Layout style (rectangular or circular)
     * @param spacingMode Spacing mode (compact, standard, loose)
     * @param viewMode View mode (grid or list)
     * @param customSpacing Custom spacing in dp (-1 to use spacingMode)
     * @return LayoutConfig with optimal layout configuration
     */
    public static LayoutConfig calculateOptimalLayout(Context context, int buttonCount,
                                                     LayoutStyle layoutStyle,
                                                     SpacingMode spacingMode,
                                                     ViewMode viewMode,
                                                     int customSpacing) {
        // Ensure button count is within valid range
        if (buttonCount < 9) buttonCount = 9;
        if (buttonCount > 25) buttonCount = 25;

        if (context == null) {
            // Fallback for null context
            return new LayoutConfig(3, (int) Math.ceil((double) buttonCount / 3), buttonCount,
                                   layoutStyle, spacingMode.spacingDp, viewMode);
        }

        try {
            // Get effective spacing (use custom spacing if provided)
            int spacing = customSpacing > 0 ? customSpacing : getEffectiveSpacing(context, spacingMode);

            // Calculate layout based on view mode
            return calculateGridLayout(context, buttonCount, layoutStyle, spacing);
        } catch (Exception e) {
            // Fallback in case of any error
            int fallbackSpacing = customSpacing > 0 ? customSpacing : spacingMode.spacingDp;
            return new LayoutConfig(3, (int) Math.ceil((double) buttonCount / 3), buttonCount,
                                   layoutStyle, fallbackSpacing, viewMode);
        }
    }



    /**
     * Check if the device is in landscape orientation
     * @param context Application context
     * @return true if landscape, false if portrait
     */
    public static boolean isLandscape(Context context) {
        DisplayMetrics displayMetrics = context.getResources().getDisplayMetrics();
        return displayMetrics.widthPixels > displayMetrics.heightPixels;
    }

    /**
     * Adjust layout for landscape orientation
     * @param context Application context
     * @param buttonCount Number of buttons
     * @return LayoutConfig optimized for landscape
     */
    public static LayoutConfig calculateLandscapeLayout(Context context, int buttonCount) {
        LayoutConfig portraitLayout = calculateOptimalLayout(context, buttonCount);
        
        // In landscape, we can afford more columns
        int adjustedColumns = Math.min(portraitLayout.columns + 1, 6);
        int adjustedRows = (int) Math.ceil((double) buttonCount / adjustedColumns);
        
        return new LayoutConfig(adjustedColumns, adjustedRows, buttonCount);
    }

    /**
     * Get effective spacing based on spacing mode and screen density
     */
    private static int getEffectiveSpacing(Context context, SpacingMode spacingMode) {
        // For now, just return the spacing from the mode
        // Could be enhanced to consider screen density
        return spacingMode.spacingDp;
    }



    /**
     * Calculate layout for grid view mode
     */
    private static LayoutConfig calculateGridLayout(Context context, int buttonCount,
                                                   LayoutStyle layoutStyle, int spacing) {
        DisplayMetrics displayMetrics = context.getResources().getDisplayMetrics();
        float screenWidthDp = displayMetrics.widthPixels / displayMetrics.density;

        // Adjust minimum button width based on layout style
        float minButtonWidth = layoutStyle == LayoutStyle.CIRCULAR ? 90f : 80f;

        // Calculate optimal columns
        int columns = calculateOptimalColumns(screenWidthDp, buttonCount, minButtonWidth);
        int rows = (int) Math.ceil((double) buttonCount / columns);

        return new LayoutConfig(columns, rows, buttonCount, layoutStyle, spacing, ViewMode.GRID);
    }

    /**
     * Calculate optimal columns with minimum button width consideration
     */
    private static int calculateOptimalColumns(float screenWidthDp, int buttonCount, float minButtonWidth) {
        // Calculate maximum possible columns based on screen width
        int maxColumns = Math.max(1, (int) (screenWidthDp / minButtonWidth));

        // Use existing logic for optimal column calculation
        if (buttonCount <= 9) {
            return Math.min(3, maxColumns);
        } else if (buttonCount <= 12) {
            return Math.min(4, maxColumns);
        } else if (buttonCount <= 16) {
            return Math.min(4, maxColumns);
        } else if (buttonCount <= 20) {
            return Math.min(screenWidthDp > 360 ? 5 : 4, maxColumns);
        } else {
            return Math.min(screenWidthDp > 480 ? 6 : 5, maxColumns);
        }
    }
}
