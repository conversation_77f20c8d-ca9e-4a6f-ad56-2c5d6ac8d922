<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="filecreation_entries">
        <item>@string/new_file_creation_onceamonth</item>
        <item>@string/new_file_creation_onceaday</item>
        <item>@string/new_file_creation_everystart</item>
        <item>@string/new_file_creation_custom</item>
    </string-array>

    <string-array name="filecreation_values">
        <item>onceamonth</item>
        <item>onceaday</item>
        <item>everystart</item>
        <item>custom</item>
    </string-array>


    <string-array name="autoemail_presets">
        <item>@string/emailprovider_google</item>
        <item>@string/emailprovider_msn</item>
        <item>@string/emailprovider_yahoo</item>
        <item>@string/emailprovider_manual</item>
    </string-array>

    <string-array name="autoemail_presetvalues">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>99</item>
    </string-array>


    <string-array name="osm_visibility_choices">
        <item>private</item>
        <item>public</item>
        <item>trackable</item>
        <item>identifiable</item>
    </string-array>

    <string-array name="opengts_server_communication_methods_values">
        <item>HTTP</item>
        <item>HTTPS</item>
        <item>UDP</item>
    </string-array>

    <string-array name="autoftp_ssltls_entries">
        <item>@string/autoftp_ssltls_none</item>
        <item>@string/autoftp_ssltls_ssl</item>
        <item>@string/autoftp_ssltls_tls</item>
    </string-array>
    <string-array name="autoftp_ssltls_values">
        <item> </item>
        <item>SSL</item>
        <item>TLS</item>
    </string-array>

    <string-array name="gps_main_views">
        <item>@string/view_simple</item>
        <item>@string/view_detailed</item>
        <item>@string/view_big</item>
        <item>@string/view_log</item>
        <item>@string/view_annotation</item>
    </string-array>


    <string-array name="app_theme_options">
        <item>@string/app_theme_follow_system</item>
        <item>@string/app_theme_light</item>
        <item>@string/app_theme_dark</item>
    </string-array>

    <string-array name="app_theme_values">
        <item>system</item>
        <item>light</item>
        <item>dark</item>
    </string-array>



    <!-- Annotation view mode options -->
    <string-array name="annotation_view_mode_entries">
        <item>Grid view</item>
        <item>Grouped view</item>
    </string-array>

    <string-array name="annotation_view_mode_values">
        <item>grid</item>
        <item>grouped</item>
    </string-array>

    <!-- Keyboard detection mode options -->
    <string-array name="keyboard_detection_mode_entries">
        <item>严格模式</item>
        <item>正常模式</item>
        <item>宽松模式</item>
        <item>强制模式</item>
    </string-array>

    <string-array name="keyboard_detection_mode_values">
        <item>STRICT</item>
        <item>NORMAL</item>
        <item>LENIENT</item>
        <item>FORCE_ALL</item>
    </string-array>

    <string-array name="haptic_feedback_intensity_entries">
        <item>低强度</item>
        <item>中强度</item>
        <item>高强度</item>
    </string-array>

    <string-array name="haptic_feedback_intensity_values">
        <item>LOW</item>
        <item>MEDIUM</item>
        <item>HIGH</item>
    </string-array>

    <!-- Audio feedback type arrays -->
    <string-array name="audio_feedback_type_entries">
        <item>无提示音</item>
        <item>系统默认音</item>
        <item>系统通知音</item>
        <item>系统铃声</item>
        <item>系统警告音</item>
        <item>自定义音频文件</item>
    </string-array>

    <string-array name="audio_feedback_type_values">
        <item>none</item>
        <item>system_default</item>
        <item>system_notification</item>
        <item>system_ringtone</item>
        <item>system_alarm</item>
        <item>custom_file</item>
    </string-array>


</resources>