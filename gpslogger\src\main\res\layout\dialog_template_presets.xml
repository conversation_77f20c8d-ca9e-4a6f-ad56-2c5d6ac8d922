<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Header with title and add button -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="选择预设模板"
            android:textSize="18sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/add_preset_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="添加模板"
            android:textSize="12sp"
            style="@style/Widget.AppCompat.Button.Borderless" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/presets_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

</LinearLayout>
