/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.dialogs;

import android.app.Dialog;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.common.slf4j.Logs;
import org.slf4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.util.ArrayList;
import java.util.List;

/**
 * Dialog for selecting template presets
 */
public class TemplatePresetsDialog extends DialogFragment {
    
    private static final Logger LOG = Logs.of(TemplatePresetsDialog.class);
    
    private OnTemplateSelectedListener listener;
    private RecyclerView recyclerView;
    private PresetAdapter adapter;
    
    public interface OnTemplateSelectedListener {
        void onTemplateSelected(String template);
    }
    
    public void setOnTemplateSelectedListener(OnTemplateSelectedListener listener) {
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        Dialog dialog = super.onCreateDialog(savedInstanceState);
        dialog.setTitle("选择预设模板");
        return dialog;
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                           @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.dialog_template_presets, container, false);

        recyclerView = view.findViewById(R.id.presets_recycler_view);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));

        Button addPresetButton = view.findViewById(R.id.add_preset_button);
        addPresetButton.setOnClickListener(v -> showAddPresetDialog());

        setupPresetList();

        return view;
    }
    
    /**
     * Setup the preset template list
     */
    private void setupPresetList() {
        List<TemplatePreset> presets = new ArrayList<>();

        // No built-in presets - user can create their own

        // Load user-defined presets
        List<TemplatePreset> userPresets = loadUserPresets();
        presets.addAll(userPresets);

        adapter = new PresetAdapter(presets);
        recyclerView.setAdapter(adapter);

        LOG.debug("Setup preset list with {} items ({} built-in, {} user-defined)",
                 presets.size(), 4, userPresets.size());
    }

    /**
     * Load user-defined presets from preferences
     */
    private List<TemplatePreset> loadUserPresets() {
        List<TemplatePreset> userPresets = new ArrayList<>();

        if (getContext() == null) {
            return userPresets;
        }

        try {
            SharedPreferences prefs = getContext().getSharedPreferences("template_presets", Context.MODE_PRIVATE);
            String presetsJson = prefs.getString("user_presets", "[]");

            JSONArray jsonArray = new JSONArray(presetsJson);
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject presetObj = jsonArray.getJSONObject(i);
                String name = presetObj.getString("name");
                String template = presetObj.getString("template");
                String description = presetObj.optString("description", "");

                userPresets.add(new TemplatePreset(name, template, description, false));
            }

            LOG.debug("Loaded {} user-defined presets", userPresets.size());
        } catch (JSONException e) {
            LOG.error("Error loading user presets", e);
        }

        return userPresets;
    }

    /**
     * Save user-defined presets to preferences
     */
    private void saveUserPresets(List<TemplatePreset> userPresets) {
        if (getContext() == null) {
            return;
        }

        try {
            JSONArray jsonArray = new JSONArray();
            for (TemplatePreset preset : userPresets) {
                if (!preset.isBuiltIn) {
                    JSONObject presetObj = new JSONObject();
                    presetObj.put("name", preset.name);
                    presetObj.put("template", preset.template);
                    presetObj.put("description", preset.description);
                    jsonArray.put(presetObj);
                }
            }

            SharedPreferences prefs = getContext().getSharedPreferences("template_presets", Context.MODE_PRIVATE);
            prefs.edit().putString("user_presets", jsonArray.toString()).apply();

            LOG.debug("Saved {} user-defined presets", jsonArray.length());
        } catch (JSONException e) {
            LOG.error("Error saving user presets", e);
        }
    }

    /**
     * Show dialog to edit a preset
     */
    private void showEditPresetDialog(TemplatePreset preset, int position) {
        EditTemplatePresetDialog editDialog = new EditTemplatePresetDialog();
        editDialog.setPreset(preset);
        editDialog.setOnPresetEditedListener((name, template, description) -> {
            // Update the preset
            preset.name = name;
            preset.template = template;
            preset.description = description;

            // Update the adapter
            adapter.notifyItemChanged(position);

            // Save to preferences
            saveUserPresets(adapter.presets);

            Toast.makeText(getContext(), "模板已更新", Toast.LENGTH_SHORT).show();
        });
        editDialog.show(getParentFragmentManager(), "edit_preset");
    }

    /**
     * Show confirmation dialog to delete a preset
     */
    private void showDeleteConfirmDialog(TemplatePreset preset, int position) {
        new androidx.appcompat.app.AlertDialog.Builder(requireContext())
                .setTitle("删除模板")
                .setMessage("确定要删除模板 \"" + preset.name + "\" 吗？")
                .setPositiveButton("删除", (dialog, which) -> {
                    // Remove from adapter
                    adapter.presets.remove(position);
                    adapter.notifyItemRemoved(position);

                    // Save to preferences
                    saveUserPresets(adapter.presets);

                    Toast.makeText(getContext(), "模板已删除", Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton("取消", null)
                .show();
    }

    /**
     * Show dialog to add a new preset
     */
    private void showAddPresetDialog() {
        EditTemplatePresetDialog addDialog = new EditTemplatePresetDialog();
        addDialog.setPreset("我的模板", "{date} {time} - {voice_text}", "自定义模板");
        addDialog.setOnPresetEditedListener((name, template, description) -> {
            // Create new preset
            TemplatePreset newPreset = new TemplatePreset(name, template, description, false);

            // Add to adapter
            adapter.presets.add(newPreset);
            adapter.notifyItemInserted(adapter.presets.size() - 1);

            // Save to preferences
            saveUserPresets(adapter.presets);

            Toast.makeText(getContext(), "模板已添加", Toast.LENGTH_SHORT).show();
        });
        addDialog.show(getParentFragmentManager(), "add_preset");
    }
    
    /**
     * Template preset data class
     */
    private static class TemplatePreset {
        String name;
        String template;
        String description;
        final boolean isBuiltIn;

        TemplatePreset(String name, String template, String description, boolean isBuiltIn) {
            this.name = name;
            this.template = template;
            this.description = description;
            this.isBuiltIn = isBuiltIn;
        }

        // Constructor for user-defined presets
        TemplatePreset(String name, String template, String description) {
            this(name, template, description, false);
        }
    }
    
    /**
     * Adapter for preset list
     */
    private class PresetAdapter extends RecyclerView.Adapter<PresetAdapter.PresetViewHolder> {
        
        private final List<TemplatePreset> presets;
        
        PresetAdapter(List<TemplatePreset> presets) {
            this.presets = presets;
        }
        
        @NonNull
        @Override
        public PresetViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_template_preset, parent, false);
            return new PresetViewHolder(view);
        }
        
        @Override
        public void onBindViewHolder(@NonNull PresetViewHolder holder, int position) {
            holder.bind(presets.get(position));
        }
        
        @Override
        public int getItemCount() {
            return presets.size();
        }
        
        /**
         * ViewHolder for preset items
         */
        class PresetViewHolder extends RecyclerView.ViewHolder {
            private final TextView nameTextView;
            private final TextView templateTextView;
            private final TextView descriptionTextView;
            private final LinearLayout actionButtonsLayout;
            private final ImageButton editButton;
            private final ImageButton deleteButton;

            PresetViewHolder(@NonNull View itemView) {
                super(itemView);
                nameTextView = itemView.findViewById(R.id.preset_name_text_view);
                templateTextView = itemView.findViewById(R.id.preset_template_text_view);
                descriptionTextView = itemView.findViewById(R.id.preset_description_text_view);
                actionButtonsLayout = itemView.findViewById(R.id.action_buttons_layout);
                editButton = itemView.findViewById(R.id.edit_preset_button);
                deleteButton = itemView.findViewById(R.id.delete_preset_button);

                itemView.setOnClickListener(v -> {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION && listener != null) {
                        TemplatePreset preset = presets.get(position);
                        listener.onTemplateSelected(preset.template);
                        dismiss();
                    }
                });

                editButton.setOnClickListener(v -> {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        TemplatePreset preset = presets.get(position);
                        showEditPresetDialog(preset, position);
                    }
                });

                deleteButton.setOnClickListener(v -> {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        TemplatePreset preset = presets.get(position);
                        showDeleteConfirmDialog(preset, position);
                    }
                });
            }

            void bind(TemplatePreset preset) {
                nameTextView.setText(preset.name);
                templateTextView.setText(preset.template);
                descriptionTextView.setText(preset.description);

                // Show action buttons only for user-defined presets
                actionButtonsLayout.setVisibility(preset.isBuiltIn ? View.GONE : View.VISIBLE);
            }
        }
    }
}
