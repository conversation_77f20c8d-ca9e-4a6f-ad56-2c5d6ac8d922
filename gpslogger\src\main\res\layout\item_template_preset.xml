<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header with name and action buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/preset_name_text_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="模板名称"
                android:textColor="@color/primaryColor"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- Action buttons for user-defined presets -->
            <LinearLayout
                android:id="@+id/action_buttons_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="gone">

                <ImageButton
                    android:id="@+id/edit_preset_button"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginEnd="8dp"
                    android:background="?android:attr/selectableItemBackgroundBorderless"
                    android:contentDescription="编辑模板"
                    android:src="@android:drawable/ic_menu_edit"
                    android:scaleType="centerInside"
                    android:padding="4dp" />

                <ImageButton
                    android:id="@+id/delete_preset_button"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="?android:attr/selectableItemBackgroundBorderless"
                    android:contentDescription="删除模板"
                    android:src="@android:drawable/ic_menu_delete"
                    android:scaleType="centerInside"
                    android:padding="4dp" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/preset_template_text_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/preview_background"
            android:fontFamily="monospace"
            android:padding="8dp"
            android:text="模板内容"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/preset_description_text_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="模板描述"
            android:textColor="@android:color/darker_gray"
            android:textSize="14sp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
