/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.common;

import android.content.Context;
import android.location.Location;
import com.mendhak.gpslogger.ui.components.template.AnnotationTemplateEngine;
import com.mendhak.gpslogger.common.slf4j.Logs;
import org.slf4j.Logger;

import java.io.File;

/**
 * Manages file path generation with support for date-based folder structure
 */
public class FilePathManager {
    
    private static final Logger LOG = Logs.of(FilePathManager.class);
    
    /**
     * Get the complete file path for a log file, including date-based folder structure if enabled
     * @param context Android context
     * @param fileName Base filename without extension
     * @param extension File extension (e.g., ".gpx", ".kml")
     * @return Complete File object for the log file
     */
    public static File getLogFilePath(Context context, String fileName, String extension) {
        PreferenceHelper preferenceHelper = PreferenceHelper.getInstance();
        File baseFolder = new File(preferenceHelper.getGpsLoggerFolder());
        
        if (preferenceHelper.shouldCreateDateFolders()) {
            try {
                // Use annotation template engine to generate folder path
                AnnotationTemplateEngine templateEngine = AnnotationTemplateEngine.getInstance();
                String folderTemplate = preferenceHelper.getFolderPathTemplate();
                
                // Process the folder path template
                String folderName = templateEngine.processTemplateString(
                    folderTemplate,
                    context,
                    null, // location not needed for date folders
                    "", // voiceText
                    "", // buttonName
                    0,  // buttonIndex
                    ""  // groupName
                );

                // Replace path separators with dashes to create a single folder name
                folderName = folderName.replace("/", "-").replace("\\", "-");

                // Create the single date-based folder
                File dateFolder = new File(baseFolder, folderName);
                if (!dateFolder.exists()) {
                    boolean created = dateFolder.mkdirs();
                    if (created) {
                        LOG.info("Created date folder: {}", dateFolder.getAbsolutePath());
                    } else {
                        LOG.warn("Failed to create date folder: {}, falling back to base folder", dateFolder.getAbsolutePath());
                        dateFolder = baseFolder;
                    }
                }
                
                return new File(dateFolder, fileName + extension);
                
            } catch (Exception e) {
                LOG.error("Error creating date-based folder structure, falling back to base folder", e);
                return new File(baseFolder, fileName + extension);
            }
        } else {
            // Traditional flat folder structure
            if (!baseFolder.exists()) {
                baseFolder.mkdirs();
            }
            return new File(baseFolder, fileName + extension);
        }
    }
    
    /**
     * Get the complete file path for a log file with location context
     * @param context Android context
     * @param location Current location (may be null)
     * @param fileName Base filename without extension
     * @param extension File extension (e.g., ".gpx", ".kml")
     * @return Complete File object for the log file
     */
    public static File getLogFilePathWithLocation(Context context, Location location, String fileName, String extension) {
        PreferenceHelper preferenceHelper = PreferenceHelper.getInstance();
        File baseFolder = new File(preferenceHelper.getGpsLoggerFolder());
        
        if (preferenceHelper.shouldCreateDateFolders()) {
            try {
                // Use annotation template engine to generate folder path
                AnnotationTemplateEngine templateEngine = AnnotationTemplateEngine.getInstance();
                String folderTemplate = preferenceHelper.getFolderPathTemplate();
                
                // Process the folder path template with location context
                String folderName = templateEngine.processTemplateString(
                    folderTemplate,
                    context,
                    location,
                    "", // voiceText
                    "", // buttonName
                    0,  // buttonIndex
                    ""  // groupName
                );

                // Replace path separators with dashes to create a single folder name
                folderName = folderName.replace("/", "-").replace("\\", "-");

                // Create the single date-based folder
                File dateFolder = new File(baseFolder, folderName);
                if (!dateFolder.exists()) {
                    boolean created = dateFolder.mkdirs();
                    if (created) {
                        LOG.info("Created date folder with location context: {}", dateFolder.getAbsolutePath());
                    } else {
                        LOG.warn("Failed to create date folder: {}, falling back to base folder", dateFolder.getAbsolutePath());
                        dateFolder = baseFolder;
                    }
                }
                
                return new File(dateFolder, fileName + extension);
                
            } catch (Exception e) {
                LOG.error("Error creating date-based folder structure with location, falling back to base folder", e);
                return new File(baseFolder, fileName + extension);
            }
        } else {
            // Traditional flat folder structure
            if (!baseFolder.exists()) {
                baseFolder.mkdirs();
            }
            return new File(baseFolder, fileName + extension);
        }
    }
    
    /**
     * Sanitize folder path to remove invalid characters
     * @param folderPath Raw folder path
     * @return Sanitized folder path
     */
    public static String sanitizeFolderPath(String folderPath) {
        if (folderPath == null || folderPath.trim().isEmpty()) {
            return "";
        }
        
        // Remove or replace invalid folder name characters
        String sanitized = folderPath.replaceAll("[\\\\:*?\"<>|]", "_")
                                    .replaceAll("\\s+", "_")
                                    .trim();
        
        // Remove leading/trailing slashes and dots
        sanitized = sanitized.replaceAll("^[/\\.]+|[/\\.]+$", "");
        
        return sanitized;
    }
}
