<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:title="外部设备控制设置">

    <!-- Main external control switch -->
    <SwitchPreferenceCompat
        android:key="external_control_enabled"
        android:title="启用外部设备控制"
        android:summary="允许通过外部设备控制GPS记录功能"
        android:defaultValue="false"
        app:iconSpaceReserved="false" />

    <!-- Hardware buttons category -->
    <PreferenceCategory
        android:title="硬件按键"
        android:key="hardware_buttons_category">

        <SwitchPreferenceCompat
            android:key="hardware_buttons_enabled"
            android:title="启用硬件按键"
            android:summary="使用音量键等硬件按键控制GPS记录"
            android:defaultValue="false"
            android:dependency="external_control_enabled"
            app:iconSpaceReserved="false" />

        <ListPreference
            android:key="volume_up_action"
            android:title="音量+键动作"
            android:summary="选择音量+键触发的功能"
            android:defaultValue="NONE"
            android:dependency="hardware_buttons_enabled"
            app:iconSpaceReserved="false" />

        <ListPreference
            android:key="volume_down_action"
            android:title="音量-键动作"
            android:summary="选择音量-键触发的功能"
            android:defaultValue="NONE"
            android:dependency="hardware_buttons_enabled"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <!-- Proximity sensor category -->
    <PreferenceCategory
        android:title="距离传感器"
        android:key="proximity_sensor_category">

        <SwitchPreferenceCompat
            android:key="proximity_sensor_enabled"
            android:title="启用距离传感器"
            android:summary="使用距离传感器检测手掌接近/远离"
            android:defaultValue="false"
            android:dependency="external_control_enabled"
            app:iconSpaceReserved="false" />

        <ListPreference
            android:key="proximity_near_action"
            android:title="接近时动作"
            android:summary="手掌接近传感器时触发的功能"
            android:defaultValue="NONE"
            android:dependency="proximity_sensor_enabled"
            app:iconSpaceReserved="false" />

        <ListPreference
            android:key="proximity_far_action"
            android:title="远离时动作"
            android:summary="手掌远离传感器时触发的功能"
            android:defaultValue="NONE"
            android:dependency="proximity_sensor_enabled"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <!-- Headset buttons category -->
    <PreferenceCategory
        android:title="耳机按键"
        android:key="headset_buttons_category">

        <SwitchPreferenceCompat
            android:key="headset_buttons_enabled"
            android:title="启用耳机按键"
            android:summary="使用有线/无线耳机按键控制GPS记录"
            android:defaultValue="false"
            android:dependency="external_control_enabled"
            app:iconSpaceReserved="false" />

        <!-- Media button gestures -->
        <PreferenceCategory
            android:title="媒体按钮手势"
            android:key="media_button_gestures_category">

            <ListPreference
                android:key="headset_media_button_single_click"
                android:title="媒体按钮 - 单击"
                android:summary="媒体按钮单击触发的功能"
                android:defaultValue="NONE"
                android:dependency="headset_buttons_enabled"
                app:iconSpaceReserved="false" />

            <ListPreference
                android:key="headset_media_button_double_click"
                android:title="媒体按钮 - 双击"
                android:summary="媒体按钮双击触发的功能"
                android:defaultValue="NONE"
                android:dependency="headset_buttons_enabled"
                app:iconSpaceReserved="false" />



        </PreferenceCategory>

        <!-- Volume up button gestures -->
        <PreferenceCategory
            android:title="音量+按钮手势"
            android:key="volume_up_gestures_category">

            <ListPreference
                android:key="headset_volume_up_single_click"
                android:title="音量+ - 单击"
                android:summary="音量+按钮单击触发的功能"
                android:defaultValue="NONE"
                android:dependency="headset_buttons_enabled"
                app:iconSpaceReserved="false" />

            <ListPreference
                android:key="headset_volume_up_double_click"
                android:title="音量+ - 双击"
                android:summary="音量+按钮双击触发的功能"
                android:defaultValue="NONE"
                android:dependency="headset_buttons_enabled"
                app:iconSpaceReserved="false" />



        </PreferenceCategory>

        <!-- Volume down button gestures -->
        <PreferenceCategory
            android:title="音量-按钮手势"
            android:key="volume_down_gestures_category">

            <ListPreference
                android:key="headset_volume_down_single_click"
                android:title="音量- - 单击"
                android:summary="音量-按钮单击触发的功能"
                android:defaultValue="NONE"
                android:dependency="headset_buttons_enabled"
                app:iconSpaceReserved="false" />

            <ListPreference
                android:key="headset_volume_down_double_click"
                android:title="音量- - 双击"
                android:summary="音量-按钮双击触发的功能"
                android:defaultValue="NONE"
                android:dependency="headset_buttons_enabled"
                app:iconSpaceReserved="false" />



        </PreferenceCategory>



    </PreferenceCategory>

    <!-- External keyboard category -->
    <PreferenceCategory
        android:title="外接键盘"
        android:key="external_keyboard_category">

        <SwitchPreferenceCompat
            android:key="external_keyboard_enabled"
            android:title="启用外接键盘"
            android:summary="使用USB/蓝牙键盘控制GPS记录"
            android:defaultValue="false"
            android:dependency="external_control_enabled"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="keyboard_custom_keys_config"
            android:title="自定义按键映射"
            android:summary="配置键盘按键对应的功能"
            android:dependency="external_keyboard_enabled"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="keyboard_test_mode"
            android:title="键盘事件测试"
            android:summary="测试键盘事件检测和处理"
            android:dependency="external_keyboard_enabled"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="keyboard_diagnostics"
            android:title="键盘诊断信息"
            android:summary="查看键盘检测状态和统计信息"
            android:dependency="external_keyboard_enabled"
            app:iconSpaceReserved="false" />

        <ListPreference
            android:key="keyboard_detection_mode"
            android:title="键盘检测模式"
            android:summary="选择键盘检测的严格程度"
            android:entries="@array/keyboard_detection_mode_entries"
            android:entryValues="@array/keyboard_detection_mode_values"
            android:defaultValue="NORMAL"
            android:dependency="external_keyboard_enabled"
            app:iconSpaceReserved="false" />



    </PreferenceCategory>

    <!-- Steering Wheel Button Settings -->
    <PreferenceCategory
        android:title="方向盘按键控制"
        app:iconSpaceReserved="false">

        <SwitchPreferenceCompat
            android:key="steering_wheel_enabled"
            android:title="启用方向盘按键"
            android:summary="通过方向盘按键控制GPS记录功能"
            android:defaultValue="false"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="steering_wheel_mappings"
            android:title="方向盘按键映射"
            android:summary="配置方向盘按键对应的功能"
            android:dependency="steering_wheel_enabled"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="steering_wheel_test"
            android:title="方向盘按键测试"
            android:summary="测试方向盘按键检测和响应"
            android:dependency="steering_wheel_enabled"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="steering_wheel_status"
            android:title="方向盘连接状态"
            android:summary="查看车机蓝牙连接和按键状态"
            android:dependency="steering_wheel_enabled"
            app:iconSpaceReserved="false" />

        <ListPreference
            android:key="steering_wheel_detection_mode"
            android:title="方向盘检测模式"
            android:summary="选择方向盘按键检测的严格程度"
            android:entries="@array/keyboard_detection_mode_entries"
            android:entryValues="@array/keyboard_detection_mode_values"
            android:defaultValue="NORMAL"
            android:dependency="steering_wheel_enabled"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <!-- Status and help -->
    <PreferenceCategory
        android:title="状态和帮助"
        android:key="status_help_category">

        <Preference
            android:key="external_control_status"
            android:title="当前状态"
            android:summary="查看外部设备连接和启用状态"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="external_control_help"
            android:title="使用帮助"
            android:summary="了解如何使用外部设备控制功能"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

</PreferenceScreen>
