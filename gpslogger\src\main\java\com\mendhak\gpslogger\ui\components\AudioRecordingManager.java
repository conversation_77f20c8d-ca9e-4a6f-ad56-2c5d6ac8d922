/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.location.Location;
import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.os.Handler;
import android.os.Looper;
import android.os.VibrationEffect;
import android.os.Vibrator;
import androidx.core.content.ContextCompat;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.FilePathManager;
import com.mendhak.gpslogger.ui.components.template.AnnotationTemplateEngine;
import org.slf4j.Logger;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Manages audio recording functionality for annotation buttons when voice input is disabled
 */
public class AudioRecordingManager {
    
    private static final Logger LOG = Logs.of(AudioRecordingManager.class);
    
    // Audio recording parameters - optimized for high quality voice recording
    private static final int SAMPLE_RATE = 44100; // 44.1kHz for high quality audio
    private static final int CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO;
    private static final int AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT;
    private static final int BUFFER_SIZE_FACTOR = 2; // Reduced buffer to minimize latency



    // Auto-stop parameters - improved for better voice detection
    private static final int DEFAULT_SILENCE_THRESHOLD = 800; // Default amplitude threshold for silence detection
    private static final long DEFAULT_SILENCE_TIMEOUT_MS = 2000; // 2 seconds of silence before auto-stop (reduced from 3s)
    private static final int VOLUME_CHECK_INTERVAL_MS = 50; // Check volume every 50ms (increased frequency)
    private static final long MIN_RECORDING_DURATION_MS = 1000; // Minimum 1 second recording

    // Advanced voice detection parameters
    private static final int NOISE_BASELINE_SAMPLES = 20; // Number of samples to establish noise baseline
    private static final double NOISE_THRESHOLD_MULTIPLIER = 2.5; // Multiplier for dynamic threshold above noise baseline
    private static final int VOICE_FREQUENCY_MIN = 85; // Minimum frequency for human voice (Hz)
    private static final int VOICE_FREQUENCY_MAX = 4000; // Maximum frequency for human voice (Hz)
    private static final double VOICE_ENERGY_RATIO_THRESHOLD = 0.3; // Minimum ratio of voice frequency energy
    private static final int CONSECUTIVE_SILENCE_REQUIRED = 3; // Number of consecutive silence detections required
    
    private final Context context;
    private final PreferenceHelper preferenceHelper;
    private AudioRecordingListener listener;
    private AudioRecord audioRecord;
    private final AtomicBoolean isRecording = new AtomicBoolean(false);
    private final ExecutorService recordingExecutor = Executors.newSingleThreadExecutor();
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    private Vibrator vibrator;
    
    // Recording state
    private File currentRecordingFile;
    private String buttonName;
    private int buttonIndex;
    private String groupName;
    private String voiceText;

    // Auto-stop mechanism state
    private long recordingStartTime;
    private long lastSoundDetectedTime;
    private final AtomicBoolean autoStopEnabled = new AtomicBoolean(true);
    private Handler autoStopHandler;

    // Advanced voice detection state
    private double noiseBaseline = 0.0; // Established noise baseline
    private final List<Double> noiseBaselineSamples = new ArrayList<>(); // Samples for establishing baseline
    private boolean noiseBaselineEstablished = false;
    private int consecutiveSilenceCount = 0; // Counter for consecutive silence detections
    private double dynamicSilenceThreshold = DEFAULT_SILENCE_THRESHOLD; // Dynamic threshold based on environment

    // Configurable parameters (can be set via preferences)
    private long silenceTimeoutMs = DEFAULT_SILENCE_TIMEOUT_MS;
    private int baseSilenceThreshold = DEFAULT_SILENCE_THRESHOLD;
    private boolean advancedDetectionEnabled = true;


    
    /**
     * Interface for audio recording callbacks
     */
    public interface AudioRecordingListener {
        void onRecordingStarted();
        void onRecordingFinished(String audioFilePath);
        void onRecordingError(String error);
        void onRecordingCancelled();
        void onRecordingProgress(long durationMs); // New callback for recording duration
        void onSilenceDetected(long silenceDurationMs); // New callback for silence detection
    }
    
    public AudioRecordingManager(Context context, AudioRecordingListener listener) {
        this.context = context;
        this.listener = listener;
        this.preferenceHelper = PreferenceHelper.getInstance();
        this.vibrator = (Vibrator) context.getSystemService(Context.VIBRATOR_SERVICE);
        this.autoStopHandler = new Handler(Looper.getMainLooper());

        // Load user preferences for recording settings
        loadRecordingPreferences();
    }

    /**
     * Load recording preferences from user settings
     */
    private void loadRecordingPreferences() {
        // Load silence timeout (default 2 seconds, user can configure 1-5 seconds)
        silenceTimeoutMs = preferenceHelper.getAudioRecordingSilenceTimeout() * 1000L;

        // Load silence threshold (default 800, user can configure 200-2000)
        baseSilenceThreshold = preferenceHelper.getAudioRecordingSilenceThreshold();

        // Load advanced detection setting
        advancedDetectionEnabled = preferenceHelper.isAudioRecordingAdvancedDetectionEnabled();

        LOG.debug("Loaded recording preferences - timeout: {}ms, threshold: {}, advanced: {}",
                 silenceTimeoutMs, baseSilenceThreshold, advancedDetectionEnabled);
    }
    
    /**
     * Check if RECORD_AUDIO permission is granted
     */
    public boolean hasRecordAudioPermission() {
        return ContextCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO) 
                == PackageManager.PERMISSION_GRANTED;
    }
    
    /**
     * Start audio recording with annotation context
     */
    public boolean startRecording(String voiceText, String buttonName, int buttonIndex, String groupName, Location location) {
        LOG.debug("Starting audio recording for button: {}", buttonName);
        
        if (!hasRecordAudioPermission()) {
            LOG.warn("RECORD_AUDIO permission not granted");
            if (listener != null) {
                listener.onRecordingError("需要录音权限才能使用录音功能");
            }
            return false;
        }
        
        if (isRecording.get()) {
            LOG.warn("Already recording, cannot start new recording");
            return false;
        }
        
        // Store annotation context
        this.voiceText = voiceText;
        this.buttonName = buttonName;
        this.buttonIndex = buttonIndex;
        this.groupName = groupName;
        
        try {
            // Generate filename using annotation template
            String fileName = generateFileName(location);
            
            // Create recording file using FilePathManager for date folder support
            currentRecordingFile = FilePathManager.getLogFilePathWithLocation(context, location, fileName, ".wav");
            LOG.debug("Recording to file: {}", currentRecordingFile.getAbsolutePath());
            
            // Initialize AudioRecord
            int bufferSize = AudioRecord.getMinBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT) * BUFFER_SIZE_FACTOR;
            audioRecord = new AudioRecord(MediaRecorder.AudioSource.MIC, SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT, bufferSize);
            
            if (audioRecord.getState() != AudioRecord.STATE_INITIALIZED) {
                LOG.error("AudioRecord initialization failed");
                if (listener != null) {
                    listener.onRecordingError("录音设备初始化失败");
                }
                return false;
            }
            
            // Start recording
            audioRecord.startRecording();
            isRecording.set(true);

            // Initialize auto-stop mechanism
            recordingStartTime = System.currentTimeMillis();
            lastSoundDetectedTime = recordingStartTime;

            // Reset voice detection state
            resetVoiceDetectionState();

            // Provide haptic feedback
            provideHapticFeedback();

            // Start recording in background thread
            recordingExecutor.submit(this::recordAudio);

            // Start progress monitoring
            startProgressMonitoring();

            // Notify listener
            if (listener != null) {
                mainHandler.post(() -> listener.onRecordingStarted());
            }
            
            LOG.debug("Audio recording started successfully");
            return true;
            
        } catch (Exception e) {
            LOG.error("Error starting audio recording", e);
            if (listener != null) {
                listener.onRecordingError("启动录音失败: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Stop audio recording
     */
    public void stopRecording() {
        LOG.debug("Stopping audio recording");
        
        if (!isRecording.get()) {
            LOG.debug("Not currently recording");
            return;
        }
        
        isRecording.set(false);

        // Stop progress monitoring
        stopProgressMonitoring();

        // Provide haptic feedback
        provideHapticFeedback();

        // AudioRecord will be stopped in the recording thread
    }
    
    /**
     * Cancel audio recording
     */
    public void cancelRecording() {
        LOG.debug("Cancelling audio recording");
        
        if (!isRecording.get()) {
            LOG.debug("Not currently recording");
            return;
        }
        
        isRecording.set(false);

        // Stop progress monitoring
        stopProgressMonitoring();

        // Delete the recording file if it exists
        if (currentRecordingFile != null && currentRecordingFile.exists()) {
            currentRecordingFile.delete();
            LOG.debug("Deleted cancelled recording file");
        }
        
        // Notify listener
        if (listener != null) {
            mainHandler.post(() -> listener.onRecordingCancelled());
        }
    }
    
    /**
     * Check if currently recording
     */
    public boolean isRecording() {
        return isRecording.get();
    }
    
    /**
     * Set the recording listener
     */
    public void setListener(AudioRecordingListener listener) {
        this.listener = listener;
    }
    
    /**
     * Clean up resources
     */
    public void cleanup() {
        stopRecording();
        recordingExecutor.shutdown();
    }
    
    /**
     * Generate filename using annotation template
     */
    private String generateFileName(Location location) {
        try {
            AnnotationTemplateEngine templateEngine = AnnotationTemplateEngine.getInstance();
            if (preferenceHelper.isAnnotationTemplateEnabled()) {
                // For audio recording mode, voice_text should be "none"
                String recordingVoiceText = "none";
                String templateResult = templateEngine.processTemplate(context, location, recordingVoiceText, buttonName, buttonIndex, groupName);
                // Clean the template result to make it suitable for filename
                return sanitizeFileName(templateResult);
            } else {
                // Fallback to simple timestamp-based naming
                return sanitizeFileName(buttonName + "_" + System.currentTimeMillis());
            }
        } catch (Exception e) {
            LOG.error("Error generating filename from template", e);
            // Fallback to simple timestamp-based naming
            return sanitizeFileName(buttonName + "_" + System.currentTimeMillis());
        }
    }
    
    /**
     * Sanitize filename to remove invalid characters
     */
    private String sanitizeFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "recording_" + System.currentTimeMillis();
        }
        
        // Remove or replace invalid filename characters
        String sanitized = fileName.replaceAll("[\\\\/:*?\"<>|]", "_")
                                  .replaceAll("\\s+", "_")
                                  .trim();
        
        // Limit length to avoid filesystem issues
        if (sanitized.length() > 100) {
            sanitized = sanitized.substring(0, 100);
        }
        
        return sanitized;
    }
    
    /**
     * Provide haptic feedback for recording events
     */
    private void provideHapticFeedback() {
        if (vibrator != null && vibrator.hasVibrator()) {
            try {
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                    vibrator.vibrate(VibrationEffect.createOneShot(50, VibrationEffect.DEFAULT_AMPLITUDE));
                } else {
                    vibrator.vibrate(50);
                }
            } catch (Exception e) {
                LOG.debug("Could not provide haptic feedback", e);
            }
        }
    }

    /**
     * Record audio in background thread
     */
    private void recordAudio() {
        LOG.debug("Starting audio recording thread");

        FileOutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(currentRecordingFile);

            // Write WAV header (will be updated later with correct size)
            writeWavHeader(outputStream, 0);

            int bufferSize = AudioRecord.getMinBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT) * BUFFER_SIZE_FACTOR;
            byte[] buffer = new byte[bufferSize];
            long totalBytesWritten = 0;

            while (isRecording.get()) {
                int bytesRead = audioRecord.read(buffer, 0, buffer.length);

                if (bytesRead > 0) {
                    // Check volume level for auto-stop mechanism
                    checkVolumeLevel(buffer, bytesRead);

                    // Write raw audio data without processing to preserve quality
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytesWritten += bytesRead;
                } else if (bytesRead == AudioRecord.ERROR_INVALID_OPERATION) {
                    LOG.error("AudioRecord invalid operation");
                    break;
                } else if (bytesRead == AudioRecord.ERROR_BAD_VALUE) {
                    LOG.error("AudioRecord bad value");
                    break;
                }
            }

            // Update WAV header with correct file size
            outputStream.close();
            outputStream = null;

            updateWavHeader(currentRecordingFile, totalBytesWritten);

            LOG.debug("Audio recording completed, total bytes: {}", totalBytesWritten);

            // Notify listener on main thread
            if (listener != null) {
                mainHandler.post(() -> listener.onRecordingFinished(currentRecordingFile.getAbsolutePath()));
            }

        } catch (IOException e) {
            LOG.error("Error during audio recording", e);
            if (listener != null) {
                mainHandler.post(() -> listener.onRecordingError("录音过程中发生错误: " + e.getMessage()));
            }
        } finally {
            // Clean up
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    LOG.debug("Error closing output stream", e);
                }
            }

            if (audioRecord != null) {
                try {
                    audioRecord.stop();
                    audioRecord.release();
                    audioRecord = null;
                } catch (Exception e) {
                    LOG.debug("Error releasing AudioRecord", e);
                }
            }

            LOG.debug("Audio recording thread finished");
        }
    }

    /**
     * Write WAV file header
     */
    private void writeWavHeader(FileOutputStream outputStream, long dataSize) throws IOException {
        long fileSize = 36 + dataSize;

        // RIFF header
        outputStream.write("RIFF".getBytes());
        outputStream.write(intToByteArray((int) fileSize), 0, 4);
        outputStream.write("WAVE".getBytes());

        // Format chunk
        outputStream.write("fmt ".getBytes());
        outputStream.write(intToByteArray(16), 0, 4); // Chunk size
        outputStream.write(shortToByteArray((short) 1), 0, 2); // Audio format (PCM)
        outputStream.write(shortToByteArray((short) 1), 0, 2); // Number of channels
        outputStream.write(intToByteArray(SAMPLE_RATE), 0, 4); // Sample rate
        outputStream.write(intToByteArray(SAMPLE_RATE * 2), 0, 4); // Byte rate
        outputStream.write(shortToByteArray((short) 2), 0, 2); // Block align
        outputStream.write(shortToByteArray((short) 16), 0, 2); // Bits per sample

        // Data chunk
        outputStream.write("data".getBytes());
        outputStream.write(intToByteArray((int) dataSize), 0, 4);
    }

    /**
     * Update WAV file header with correct file size
     */
    private void updateWavHeader(File file, long dataSize) {
        try (java.io.RandomAccessFile raf = new java.io.RandomAccessFile(file, "rw")) {
            // Update file size in RIFF header
            raf.seek(4);
            raf.write(intToByteArray((int) (36 + dataSize)));

            // Update data size in data chunk
            raf.seek(40);
            raf.write(intToByteArray((int) dataSize));

        } catch (IOException e) {
            LOG.error("Error updating WAV header", e);
        }
    }

    /**
     * Convert int to byte array (little endian)
     */
    private byte[] intToByteArray(int value) {
        return ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN).putInt(value).array();
    }

    /**
     * Convert short to byte array (little endian)
     */
    private byte[] shortToByteArray(short value) {
        return ByteBuffer.allocate(2).order(ByteOrder.LITTLE_ENDIAN).putShort(value).array();
    }





    /**
     * Advanced voice detection and silence analysis
     * Uses multiple techniques to distinguish human voice from background noise
     */
    private void checkVolumeLevel(byte[] buffer, int length) {
        // Convert bytes to shorts for amplitude analysis
        short[] samples = new short[length / 2];
        ByteBuffer.wrap(buffer, 0, length).order(ByteOrder.LITTLE_ENDIAN).asShortBuffer().get(samples);

        // Calculate RMS (Root Mean Square) amplitude
        double rms = calculateRMS(samples);

        // Establish noise baseline if not yet done
        if (!noiseBaselineEstablished) {
            establishNoiseBaseline(rms);
            return; // Don't process voice detection until baseline is established
        }

        // Perform voice detection (advanced or simple based on user preference)
        boolean voiceDetected = advancedDetectionEnabled ?
            detectVoiceActivity(samples, rms) :
            detectSimpleVoiceActivity(rms);

        if (voiceDetected) {
            lastSoundDetectedTime = System.currentTimeMillis();
            consecutiveSilenceCount = 0; // Reset silence counter
            LOG.debug("Voice activity detected - RMS: {:.2f}, Threshold: {:.2f}",
                     rms, dynamicSilenceThreshold);
        } else {
            consecutiveSilenceCount++;
            LOG.debug("Silence detected - consecutive count: {}, RMS: {:.2f}",
                     consecutiveSilenceCount, rms);
        }
    }

    /**
     * Start progress monitoring for auto-stop and duration tracking
     */
    private void startProgressMonitoring() {
        if (autoStopHandler != null) {
            autoStopHandler.post(progressMonitoringRunnable);
        }
    }

    /**
     * Stop progress monitoring
     */
    private void stopProgressMonitoring() {
        if (autoStopHandler != null) {
            autoStopHandler.removeCallbacks(progressMonitoringRunnable);
        }
    }

    /**
     * Runnable for monitoring recording progress and auto-stop
     */
    private final Runnable progressMonitoringRunnable = new Runnable() {
        @Override
        public void run() {
            if (!isRecording.get()) {
                return;
            }

            long currentTime = System.currentTimeMillis();
            long recordingDuration = currentTime - recordingStartTime;
            long silenceDuration = currentTime - lastSoundDetectedTime;

            // Notify listener about recording progress
            if (listener != null) {
                listener.onRecordingProgress(recordingDuration);
            }

            // Check for auto-stop conditions with improved logic
            if (autoStopEnabled.get() &&
                recordingDuration > MIN_RECORDING_DURATION_MS &&
                (silenceDuration > silenceTimeoutMs || consecutiveSilenceCount >= CONSECUTIVE_SILENCE_REQUIRED)) {

                LOG.debug("Auto-stopping recording - silence duration: {}ms, consecutive silence: {}",
                         silenceDuration, consecutiveSilenceCount);

                // Notify listener about silence detection
                if (listener != null) {
                    listener.onSilenceDetected(silenceDuration);
                }

                // Auto-stop recording
                stopRecording();
                return;
            }

            // Schedule next check
            if (autoStopHandler != null) {
                autoStopHandler.postDelayed(this, VOLUME_CHECK_INTERVAL_MS);
            }
        }
    };

    /**
     * Enable or disable auto-stop functionality
     */
    public void setAutoStopEnabled(boolean enabled) {
        autoStopEnabled.set(enabled);
        LOG.debug("Auto-stop enabled: {}", enabled);
    }

    /**
     * Check if auto-stop is enabled
     */
    public boolean isAutoStopEnabled() {
        return autoStopEnabled.get();
    }

    /**
     * Reset voice detection state for new recording
     */
    private void resetVoiceDetectionState() {
        noiseBaseline = 0.0;
        noiseBaselineSamples.clear();
        noiseBaselineEstablished = false;
        consecutiveSilenceCount = 0;
        dynamicSilenceThreshold = baseSilenceThreshold;
        LOG.debug("Voice detection state reset");
    }

    /**
     * Calculate RMS (Root Mean Square) amplitude from audio samples
     */
    private double calculateRMS(short[] samples) {
        long sum = 0;
        for (short sample : samples) {
            sum += sample * sample;
        }
        return Math.sqrt(sum / (double) samples.length);
    }

    /**
     * Establish noise baseline from initial audio samples
     */
    private void establishNoiseBaseline(double rms) {
        noiseBaselineSamples.add(rms);

        if (noiseBaselineSamples.size() >= NOISE_BASELINE_SAMPLES) {
            // Calculate average noise level
            double sum = 0;
            for (double sample : noiseBaselineSamples) {
                sum += sample;
            }
            noiseBaseline = sum / noiseBaselineSamples.size();

            // Set dynamic threshold based on noise baseline
            dynamicSilenceThreshold = Math.max(
                baseSilenceThreshold,
                noiseBaseline * NOISE_THRESHOLD_MULTIPLIER
            );

            noiseBaselineEstablished = true;
            LOG.debug("Noise baseline established - baseline: {:.2f}, dynamic threshold: {:.2f}",
                     noiseBaseline, dynamicSilenceThreshold);
        }
    }

    /**
     * Simple voice activity detection using only RMS threshold
     */
    private boolean detectSimpleVoiceActivity(double rms) {
        return rms > dynamicSilenceThreshold;
    }

    /**
     * Advanced voice activity detection using multiple criteria
     */
    private boolean detectVoiceActivity(short[] samples, double rms) {
        // Criterion 1: RMS amplitude above dynamic threshold
        boolean amplitudeCheck = rms > dynamicSilenceThreshold;

        // Criterion 2: Voice frequency analysis (simplified)
        boolean frequencyCheck = analyzeVoiceFrequencies(samples);

        // Criterion 3: Energy distribution analysis
        boolean energyCheck = analyzeEnergyDistribution(samples);

        // Voice detected if at least 2 out of 3 criteria are met
        int criteriaCount = (amplitudeCheck ? 1 : 0) + (frequencyCheck ? 1 : 0) + (energyCheck ? 1 : 0);
        boolean voiceDetected = criteriaCount >= 2;

        LOG.debug("Voice detection - Amplitude: {}, Frequency: {}, Energy: {}, Result: {}",
                 amplitudeCheck, frequencyCheck, energyCheck, voiceDetected);

        return voiceDetected;
    }

    /**
     * Simplified voice frequency analysis
     * Checks if significant energy exists in human voice frequency range
     */
    private boolean analyzeVoiceFrequencies(short[] samples) {
        // This is a simplified approach - in a full implementation,
        // we would use FFT to analyze frequency spectrum

        // For now, we use a simple high-pass filter approach
        // to detect if there's significant variation in the signal
        // which is characteristic of voice vs. constant background noise

        if (samples.length < 4) return false;

        double variation = 0;
        for (int i = 1; i < samples.length; i++) {
            variation += Math.abs(samples[i] - samples[i-1]);
        }

        double avgVariation = variation / (samples.length - 1);

        // Voice typically has more variation than constant background noise
        return avgVariation > (dynamicSilenceThreshold * 0.1);
    }

    /**
     * Analyze energy distribution to distinguish voice from noise
     */
    private boolean analyzeEnergyDistribution(short[] samples) {
        if (samples.length < 10) return false;

        // Calculate energy in different segments
        int segmentSize = samples.length / 4;
        double[] segmentEnergies = new double[4];

        for (int seg = 0; seg < 4; seg++) {
            int start = seg * segmentSize;
            int end = Math.min(start + segmentSize, samples.length);

            double energy = 0;
            for (int i = start; i < end; i++) {
                energy += samples[i] * samples[i];
            }
            segmentEnergies[seg] = energy / (end - start);
        }

        // Voice typically has uneven energy distribution
        // Calculate coefficient of variation
        double mean = 0;
        for (double energy : segmentEnergies) {
            mean += energy;
        }
        mean /= segmentEnergies.length;

        if (mean < dynamicSilenceThreshold * dynamicSilenceThreshold) {
            return false; // Too quiet overall
        }

        double variance = 0;
        for (double energy : segmentEnergies) {
            variance += (energy - mean) * (energy - mean);
        }
        variance /= segmentEnergies.length;

        double coefficientOfVariation = Math.sqrt(variance) / mean;

        // Voice has higher variation than constant noise
        return coefficientOfVariation > VOICE_ENERGY_RATIO_THRESHOLD;
    }

    /**
     * Update recording configuration from preferences
     */
    public void updateConfiguration() {
        loadRecordingPreferences();
        LOG.debug("Recording configuration updated");
    }
}
