/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.fragments.settings;

import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.ui.components.template.AnnotationTemplateEngine;
import com.mendhak.gpslogger.ui.components.template.TemplateParser;
// import com.mendhak.gpslogger.ui.dialogs.VariableSelectorDialog;
// import com.mendhak.gpslogger.ui.dialogs.TemplatePresetsDialog;
import org.slf4j.Logger;

/**
 * Fragment for editing annotation templates
 */
public class AnnotationTemplateFragment extends Fragment {
    
    private static final Logger LOG = Logs.of(AnnotationTemplateFragment.class);
    
    private EditText templateEditText;
    private TextView previewTextView;
    private TextView validationTextView;
    private Switch enabledSwitch;
    private Button variableSelectorButton;
    private Button presetsButton;
    private Button configUserVariablesButton;
    private Button manageCountersButton;
    private Button configNumericCountersButton;
    private Button saveButton;
    private Button resetButton;
    private Button saveAsPresetButton;
    
    private PreferenceHelper preferenceHelper;
    private AnnotationTemplateEngine templateEngine;
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setHasOptionsMenu(true);
        
        preferenceHelper = PreferenceHelper.getInstance();
        templateEngine = AnnotationTemplateEngine.getInstance();
        
        LOG.debug("AnnotationTemplateFragment created");
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                           @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_annotation_template, container, false);

        initializeViews(view);
        setupListeners();
        loadCurrentSettings();

        return view;
    }
    
    /**
     * Initialize all views
     */
    private void initializeViews(View view) {
        templateEditText = view.findViewById(R.id.template_edit_text);
        previewTextView = view.findViewById(R.id.preview_text_view);
        validationTextView = view.findViewById(R.id.validation_text_view);
        enabledSwitch = view.findViewById(R.id.enabled_switch);
        variableSelectorButton = view.findViewById(R.id.variable_selector_button);
        presetsButton = view.findViewById(R.id.presets_button);
        configUserVariablesButton = view.findViewById(R.id.config_user_variables_button);
        manageCountersButton = view.findViewById(R.id.manage_counters_button);
        configNumericCountersButton = view.findViewById(R.id.config_numeric_counters_button);
        saveButton = view.findViewById(R.id.save_button);
        resetButton = view.findViewById(R.id.reset_button);
        saveAsPresetButton = view.findViewById(R.id.save_as_preset_button);

        LOG.debug("Views initialized");
    }
    
    /**
     * Setup event listeners
     */
    private void setupListeners() {
        // Template text change listener
        templateEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
            
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}
            
            @Override
            public void afterTextChanged(Editable s) {
                updatePreviewAndValidation();
            }
        });
        
        // Enable/disable switch
        enabledSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            updateUIState();
        });
        
        // Variable selector button
        variableSelectorButton.setOnClickListener(v -> showVariableSelector());
        
        // Presets button
        presetsButton.setOnClickListener(v -> showTemplatePresets());

        // Config user variables button
        configUserVariablesButton.setOnClickListener(v -> showUserVariableConfigDialog());

        // Manage counters button
        manageCountersButton.setOnClickListener(v -> showCounterManagerDialog());

        // Config numeric counters button
        configNumericCountersButton.setOnClickListener(v -> showNumericCounterConfigDialog());

        // Save button
        saveButton.setOnClickListener(v -> saveTemplate());
        
        // Reset button
        resetButton.setOnClickListener(v -> resetTemplate());

        // Save as preset button
        saveAsPresetButton.setOnClickListener(v -> showSaveAsPresetDialog());

        LOG.debug("Listeners setup completed");
    }
    
    /**
     * Load current settings from preferences
     */
    private void loadCurrentSettings() {
        boolean enabled = preferenceHelper.isAnnotationTemplateEnabled();
        String template = preferenceHelper.getAnnotationTemplate();
        
        enabledSwitch.setChecked(enabled);
        templateEditText.setText(template);
        
        updateUIState();
        updatePreviewAndValidation();
        
        LOG.debug("Loaded current settings: enabled={}, template='{}'", enabled, template);
    }
    
    /**
     * Update UI state based on enabled switch
     */
    private void updateUIState() {
        boolean enabled = enabledSwitch.isChecked();
        
        templateEditText.setEnabled(enabled);
        variableSelectorButton.setEnabled(enabled);
        presetsButton.setEnabled(enabled);
        configUserVariablesButton.setEnabled(enabled);
        manageCountersButton.setEnabled(enabled);
        configNumericCountersButton.setEnabled(enabled);
        saveButton.setEnabled(enabled);
        resetButton.setEnabled(enabled);
        
        if (enabled) {
            updatePreviewAndValidation();
        } else {
            previewTextView.setText("模板功能已禁用");
            validationTextView.setText("");
        }
    }
    
    /**
     * Update preview and validation
     */
    private void updatePreviewAndValidation() {
        if (!enabledSwitch.isChecked()) {
            return;
        }
        
        String template = templateEditText.getText().toString();
        
        // Validate template
        TemplateParser.ValidationResult validation = templateEngine.validateTemplate(template);
        if (validation.isValid()) {
            validationTextView.setText("✓ 模板语法正确");
            validationTextView.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
            
            // Generate preview
            try {
                String preview = templateEngine.previewTemplate(template, getContext());
                previewTextView.setText("预览: " + preview);
            } catch (Exception e) {
                previewTextView.setText("预览生成失败: " + e.getMessage());
            }
        } else {
            validationTextView.setText("✗ " + validation.getErrorMessage());
            validationTextView.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
            previewTextView.setText("模板语法错误，无法生成预览");
        }
    }
    
    /**
     * Show variable selector dialog
     */
    private void showVariableSelector() {
        com.mendhak.gpslogger.ui.dialogs.VariableSelectorDialog dialog =
            new com.mendhak.gpslogger.ui.dialogs.VariableSelectorDialog();
        dialog.setOnVariableSelectedListener(variable -> {
            insertVariableAtCursor(variable);
        });
        dialog.show(getParentFragmentManager(), "variable_selector");
    }

    /**
     * Show template presets dialog
     */
    private void showTemplatePresets() {
        com.mendhak.gpslogger.ui.dialogs.TemplatePresetsDialog dialog =
            new com.mendhak.gpslogger.ui.dialogs.TemplatePresetsDialog();
        dialog.setOnTemplateSelectedListener(template -> {
            templateEditText.setText(template);
        });
        dialog.show(getParentFragmentManager(), "template_presets");
    }

    /**
     * Show user variable configuration dialog
     */
    private void showUserVariableConfigDialog() {
        com.mendhak.gpslogger.ui.dialogs.UserVariableConfigDialog dialog =
            new com.mendhak.gpslogger.ui.dialogs.UserVariableConfigDialog();
        dialog.setOnVariablesUpdatedListener(() -> {
            // Refresh preview to show updated variable values
            updatePreviewAndValidation();
        });
        dialog.show(getParentFragmentManager(), "user_variable_config");
    }

    /**
     * Show counter manager dialog
     */
    private void showCounterManagerDialog() {
        com.mendhak.gpslogger.ui.dialogs.CounterManagerDialog dialog =
            new com.mendhak.gpslogger.ui.dialogs.CounterManagerDialog();
        dialog.setOnCountersUpdatedListener(() -> {
            // Refresh preview to show updated counter values
            updatePreviewAndValidation();
        });
        dialog.show(getParentFragmentManager(), "counter_manager");
    }

    /**
     * Show numeric counter configuration dialog
     */
    private void showNumericCounterConfigDialog() {
        com.mendhak.gpslogger.ui.dialogs.NumericCounterConfigDialog dialog =
            new com.mendhak.gpslogger.ui.dialogs.NumericCounterConfigDialog();
        dialog.setOnCountersUpdatedListener(() -> {
            // Refresh preview to show updated counter values
            updatePreviewAndValidation();
        });
        dialog.show(getParentFragmentManager(), "numeric_counter_config");
    }
    
    /**
     * Insert variable at cursor position
     */
    private void insertVariableAtCursor(String variable) {
        int start = templateEditText.getSelectionStart();
        int end = templateEditText.getSelectionEnd();
        
        String currentText = templateEditText.getText().toString();
        String newText = currentText.substring(0, start) + "{" + variable + "}" + currentText.substring(end);
        
        templateEditText.setText(newText);
        templateEditText.setSelection(start + variable.length() + 2);
    }
    
    /**
     * Save template to preferences
     */
    private void saveTemplate() {
        String template = templateEditText.getText().toString();
        boolean enabled = enabledSwitch.isChecked();
        
        // Validate before saving
        if (enabled && !templateEngine.validateTemplate(template).isValid()) {
            Toast.makeText(getContext(), "模板语法错误，无法保存", Toast.LENGTH_SHORT).show();
            return;
        }
        
        preferenceHelper.setAnnotationTemplate(template);
        preferenceHelper.setAnnotationTemplateEnabled(enabled);
        
        Toast.makeText(getContext(), "模板已保存", Toast.LENGTH_SHORT).show();
        LOG.info("Template saved: enabled={}, template='{}'", enabled, template);
    }
    
    /**
     * Reset template to default
     */
    private void resetTemplate() {
        templateEditText.setText("{voice_text}");
        updatePreviewAndValidation();
    }

    /**
     * Show dialog to save current template as preset
     */
    private void showSaveAsPresetDialog() {
        String currentTemplate = templateEditText.getText().toString().trim();

        if (currentTemplate.isEmpty()) {
            Toast.makeText(getContext(), "请先输入模板内容", Toast.LENGTH_SHORT).show();
            return;
        }

        // Validate template before saving
        TemplateParser.ValidationResult validation = templateEngine.validateTemplate(currentTemplate);
        if (!validation.isValid()) {
            Toast.makeText(getContext(), "模板语法错误，无法保存: " + validation.getErrorMessage(),
                          Toast.LENGTH_LONG).show();
            return;
        }

        com.mendhak.gpslogger.ui.dialogs.EditTemplatePresetDialog saveDialog =
            new com.mendhak.gpslogger.ui.dialogs.EditTemplatePresetDialog();
        saveDialog.setPreset("我的模板", currentTemplate, "");
        saveDialog.setOnPresetEditedListener((name, template, description) -> {
            // Save to user presets
            saveUserPreset(name, template, description);
            Toast.makeText(getContext(), "模板已保存为预设", Toast.LENGTH_SHORT).show();
        });
        saveDialog.show(getParentFragmentManager(), "save_as_preset");
    }

    /**
     * Save a user preset to preferences
     */
    private void saveUserPreset(String name, String template, String description) {
        try {
            android.content.SharedPreferences prefs = getContext().getSharedPreferences("template_presets",
                                                                                        android.content.Context.MODE_PRIVATE);
            String presetsJson = prefs.getString("user_presets", "[]");

            org.json.JSONArray jsonArray = new org.json.JSONArray(presetsJson);

            // Create new preset object
            org.json.JSONObject presetObj = new org.json.JSONObject();
            presetObj.put("name", name);
            presetObj.put("template", template);
            presetObj.put("description", description);

            jsonArray.put(presetObj);

            // Save back to preferences
            prefs.edit().putString("user_presets", jsonArray.toString()).apply();

            LOG.debug("Saved user preset: {}", name);
        } catch (org.json.JSONException e) {
            LOG.error("Error saving user preset", e);
            Toast.makeText(getContext(), "保存失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    @Override
    public void onCreateOptionsMenu(@NonNull Menu menu, @NonNull MenuInflater inflater) {
        inflater.inflate(R.menu.menu_annotation_template, menu);
        super.onCreateOptionsMenu(menu, inflater);
    }
    
    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();
        
        if (itemId == R.id.action_save) {
            saveTemplate();
            return true;
        } else if (itemId == R.id.action_import) {
            importTemplate();
            return true;
        } else if (itemId == R.id.action_export) {
            exportTemplate();
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    /**
     * Import template from clipboard
     */
    private void importTemplate() {
        try {
            android.content.ClipboardManager clipboard =
                (android.content.ClipboardManager) getContext().getSystemService(Context.CLIPBOARD_SERVICE);

            if (clipboard != null && clipboard.hasPrimaryClip()) {
                android.content.ClipData.Item item = clipboard.getPrimaryClip().getItemAt(0);
                String clipText = item.getText().toString();

                if (!clipText.isEmpty()) {
                    // Validate the template before importing
                    TemplateParser.ValidationResult validation = templateEngine.validateTemplate(clipText);
                    if (validation.isValid()) {
                        templateEditText.setText(clipText);
                        Toast.makeText(getContext(), "模板导入成功", Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(getContext(), "导入失败：" + validation.getErrorMessage(), Toast.LENGTH_LONG).show();
                    }
                } else {
                    Toast.makeText(getContext(), "剪贴板为空", Toast.LENGTH_SHORT).show();
                }
            } else {
                Toast.makeText(getContext(), "剪贴板中没有内容", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            Toast.makeText(getContext(), "导入失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Export template to clipboard
     */
    private void exportTemplate() {
        try {
            String template = templateEditText.getText().toString();
            if (template.isEmpty()) {
                Toast.makeText(getContext(), "模板为空，无法导出", Toast.LENGTH_SHORT).show();
                return;
            }

            android.content.ClipboardManager clipboard =
                (android.content.ClipboardManager) getContext().getSystemService(Context.CLIPBOARD_SERVICE);

            if (clipboard != null) {
                android.content.ClipData clip = android.content.ClipData.newPlainText("Annotation Template", template);
                clipboard.setPrimaryClip(clip);
                Toast.makeText(getContext(), "模板已复制到剪贴板", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(getContext(), "无法访问剪贴板", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            Toast.makeText(getContext(), "导出失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
}
