/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.GridLayout;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.ui.components.LayoutEnums.*;

/**
 * Preview component for annotation button layout settings
 */
public class AnnotationPreviewView extends LinearLayout {
    
    private GridLayout previewGrid;
    private Context context;
    
    public AnnotationPreviewView(Context context) {
        super(context);
        init(context);
    }
    
    public AnnotationPreviewView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }
    
    public AnnotationPreviewView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }
    
    private void init(Context context) {
        this.context = context;
        LayoutInflater.from(context).inflate(R.layout.annotation_preview_layout, this, true);
        previewGrid = findViewById(R.id.preview_grid);
        
        // Create initial preview
        updatePreview(LayoutStyle.RECTANGULAR, SpacingMode.STANDARD, ViewMode.GRID);
    }
    
    /**
     * Update the preview with new settings
     */
    public void updatePreview(LayoutStyle layoutStyle, SpacingMode spacingMode, ViewMode viewMode) {
        previewGrid.removeAllViews();
        
        // Always create grid preview since LIST mode is removed
        createGridPreview(layoutStyle, spacingMode);
    }
    
    /**
     * Create grid preview
     */
    private void createGridPreview(LayoutStyle layoutStyle, SpacingMode spacingMode) {
        previewGrid.setColumnCount(3);
        previewGrid.setRowCount(2);
        
        // Create 6 preview buttons
        for (int i = 0; i < 6; i++) {
            TextView previewButton = createPreviewButton(layoutStyle, spacingMode);
            previewButton.setText("B" + (i + 1));
            
            GridLayout.LayoutParams params = new GridLayout.LayoutParams();
            params.width = 0;
            params.height = dpToPx(40);
            params.columnSpec = GridLayout.spec(GridLayout.UNDEFINED, 1f);
            params.rowSpec = GridLayout.spec(GridLayout.UNDEFINED);
            params.setMargins(spacingMode.spacingDp, spacingMode.spacingDp, 
                             spacingMode.spacingDp, spacingMode.spacingDp);
            
            previewButton.setLayoutParams(params);
            previewGrid.addView(previewButton);
        }
    }
    

    
    /**
     * Create a preview button with the specified style
     */
    private TextView createPreviewButton(LayoutStyle layoutStyle, SpacingMode spacingMode) {
        TextView button = new TextView(context);
        button.setTextSize(10);
        button.setTextColor(Color.WHITE);
        button.setGravity(android.view.Gravity.CENTER);
        button.setBackgroundColor(Color.GRAY);
        
        // Apply layout style
        switch (layoutStyle) {
            case CIRCULAR:
                // Create circular background
                android.graphics.drawable.GradientDrawable circularBackground = 
                    new android.graphics.drawable.GradientDrawable();
                circularBackground.setShape(android.graphics.drawable.GradientDrawable.OVAL);
                circularBackground.setColor(Color.GRAY);
                circularBackground.setStroke(1, Color.LTGRAY);
                button.setBackground(circularBackground);
                break;
                
            case RECTANGULAR:
            default:
                // Create rectangular background
                android.graphics.drawable.GradientDrawable rectBackground = 
                    new android.graphics.drawable.GradientDrawable();
                rectBackground.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);
                rectBackground.setColor(Color.GRAY);
                rectBackground.setCornerRadius(dpToPx(4));
                button.setBackground(rectBackground);
                break;
        }
        
        return button;
    }
    
    /**
     * Convert dp to pixels
     */
    private int dpToPx(int dp) {
        return Math.round(dp * context.getResources().getDisplayMetrics().density);
    }
}
