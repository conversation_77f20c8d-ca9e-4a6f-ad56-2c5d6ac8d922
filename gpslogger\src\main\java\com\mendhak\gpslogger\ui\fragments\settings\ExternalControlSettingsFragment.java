/*
 * Copyright (C) 2024 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.fragments.settings;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.view.InputDevice;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.preference.ListPreference;
import androidx.preference.Preference;
import androidx.preference.PreferenceFragmentCompat;
import androidx.preference.SwitchPreferenceCompat;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.json.JSONArray;
import org.json.JSONObject;

import com.mendhak.gpslogger.GpsMainActivity;
import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.PreferenceNames;
import com.mendhak.gpslogger.common.events.ExternalControlEvents.ButtonAction;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.ui.components.external.ExternalControlManager;
import com.mendhak.gpslogger.ui.components.external.ExternalKeyboardManager;
import com.mendhak.gpslogger.ui.components.external.HeadsetButtonManager;
import com.mendhak.gpslogger.ui.components.external.ExternalControlPermissionManager;
import com.mendhak.gpslogger.ui.components.external.DeviceDetectionInterface;
import com.mendhak.gpslogger.ui.components.external.SteeringWheelButtonManager;

import org.slf4j.Logger;

/**
 * Settings fragment for external control devices
 */
public class ExternalControlSettingsFragment extends PreferenceFragmentCompat 
        implements Preference.OnPreferenceChangeListener, Preference.OnPreferenceClickListener {
    
    private static final Logger LOG = Logs.of(ExternalControlSettingsFragment.class);
    
    private PreferenceHelper preferenceHelper;
    private ExternalControlManager externalControlManager;

    // Permission request codes
    private static final int REQUEST_BLUETOOTH_PERMISSIONS = 1001;

    @Override
    public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
        setPreferencesFromResource(R.xml.external_control_settings, rootKey);

        preferenceHelper = PreferenceHelper.getInstance();

        // Get the ExternalControlManager from the Activity instead of creating a new one
        if (getActivity() instanceof GpsMainActivity) {
            GpsMainActivity mainActivity = (GpsMainActivity) getActivity();
            externalControlManager = mainActivity.getExternalControlManager();
            LOG.info("Using ExternalControlManager from Activity: {}", externalControlManager != null);
        }

        // Fallback: create new instance if we can't get it from Activity
        if (externalControlManager == null) {
            LOG.warn("Could not get ExternalControlManager from Activity, creating new instance");
            externalControlManager = new ExternalControlManager(getContext());
        }

        setupPreferences();
    }

    private void setupPreferences() {
        // Setup action list preferences
        setupActionListPreference(PreferenceNames.VOLUME_UP_ACTION);
        setupActionListPreference(PreferenceNames.VOLUME_DOWN_ACTION);
        setupActionListPreference(PreferenceNames.PROXIMITY_NEAR_ACTION);
        setupActionListPreference(PreferenceNames.PROXIMITY_FAR_ACTION);

        // Setup headset gesture preferences
        setupHeadsetGesturePreferences();
        
        // Setup click listeners
        findPreference("keyboard_custom_keys_config").setOnPreferenceClickListener(this);
        findPreference("keyboard_test_mode").setOnPreferenceClickListener(this);
        findPreference("keyboard_diagnostics").setOnPreferenceClickListener(this);
        findPreference("external_control_status").setOnPreferenceClickListener(this);
        findPreference("external_control_help").setOnPreferenceClickListener(this);

        // Setup steering wheel click listeners
        findPreference("steering_wheel_mappings").setOnPreferenceClickListener(this);
        findPreference("steering_wheel_test").setOnPreferenceClickListener(this);
        findPreference("steering_wheel_status").setOnPreferenceClickListener(this);
        
        // Setup change listeners for switches
        findPreference(PreferenceNames.EXTERNAL_CONTROL_ENABLED).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.HARDWARE_BUTTONS_ENABLED).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.PROXIMITY_SENSOR_ENABLED).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.HEADSET_BUTTONS_ENABLED).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.EXTERNAL_KEYBOARD_ENABLED).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.STEERING_WHEEL_ENABLED).setOnPreferenceChangeListener(this);
        
        updatePreferenceSummaries();
    }

    private void setupActionListPreference(String key) {
        ListPreference preference = findPreference(key);
        if (preference != null) {
            // Set up entries and values for button actions
            setupActionListEntries(preference);
            preference.setOnPreferenceChangeListener(this);

            // Set current value
            String currentValue = preferenceHelper.getString(key, ButtonAction.NONE.name());
            preference.setValue(currentValue);
            updateActionPreferenceSummary(preference, currentValue);
        }
    }

    private void setupActionListEntries(ListPreference preference) {
        // Define allowed basic actions (only the ones user wants)
        List<ButtonAction> allowedActions = new ArrayList<>();
        allowedActions.add(ButtonAction.START_STOP_LOGGING);  // 开始/停止记录
        allowedActions.add(ButtonAction.LOG_SINGLE_POINT);    // 记录单点
        allowedActions.add(ButtonAction.QUICK_VOICE_INPUT);   // 一键语音输入
        allowedActions.add(ButtonAction.QUICK_TEXT_INPUT);    // 一键文本输入

        // Get annotation buttons with their actual text
        List<AnnotationButtonInfo> annotationButtons = getAnnotationButtonsInfo();

        // Calculate total entries
        int totalEntries = allowedActions.size() + annotationButtons.size();
        String[] entries = new String[totalEntries];
        String[] entryValues = new String[totalEntries];

        // Add allowed basic actions
        for (int i = 0; i < allowedActions.size(); i++) {
            entries[i] = allowedActions.get(i).getDisplayName();
            entryValues[i] = allowedActions.get(i).name();
        }

        // Add annotation button actions with actual button text
        int index = allowedActions.size();
        for (AnnotationButtonInfo buttonInfo : annotationButtons) {
            entries[index] = buttonInfo.displayName;
            entryValues[index] = buttonInfo.actionName;
            index++;
        }

        preference.setEntries(entries);
        preference.setEntryValues(entryValues);
    }

    private void updateActionPreferenceSummary(ListPreference preference, String value) {
        ButtonAction action = ButtonAction.fromString(value);
        String displayName = action.getDisplayName();

        // For annotation button actions, try to get the actual button text
        if (action.isAnnotationButton()) {
            List<AnnotationButtonInfo> buttonInfos = getAnnotationButtonsInfo();
            for (AnnotationButtonInfo info : buttonInfos) {
                if (info.actionName.equals(value)) {
                    displayName = info.displayName;
                    break;
                }
            }
        }

        preference.setSummary(displayName);
    }

    private void updatePreferenceSummaries() {
        // Update status preference
        Preference statusPref = findPreference("external_control_status");
        if (statusPref != null && externalControlManager != null) {
            externalControlManager.initialize();
            statusPref.setSummary(externalControlManager.getStatusSummary());
        }
        
        // Update proximity sensor availability
        SwitchPreferenceCompat proximitySensorPref = findPreference(PreferenceNames.PROXIMITY_SENSOR_ENABLED);
        if (proximitySensorPref != null) {
            if (externalControlManager != null && 
                externalControlManager.getProximitySensorManager() != null &&
                !externalControlManager.getProximitySensorManager().isAvailable()) {
                proximitySensorPref.setEnabled(false);
                proximitySensorPref.setSummary("此设备不支持距离传感器");
            }
        }
    }

    @Override
    public boolean onPreferenceChange(Preference preference, Object newValue) {
        String key = preference.getKey();
        
        LOG.debug("Preference changed: {} = {}", key, newValue);
        
        if (key.equals(PreferenceNames.EXTERNAL_CONTROL_ENABLED)) {
            boolean enabled = (Boolean) newValue;
            if (externalControlManager != null) {
                externalControlManager.setExternalControlEnabled(enabled);
            }
            return true;
        }
        
        if (key.equals(PreferenceNames.HARDWARE_BUTTONS_ENABLED)) {
            boolean enabled = (Boolean) newValue;
            if (externalControlManager != null && 
                externalControlManager.getHardwareButtonManager() != null) {
                externalControlManager.getHardwareButtonManager().setEnabled(enabled);
            }
            return true;
        }
        
        if (key.equals(PreferenceNames.PROXIMITY_SENSOR_ENABLED)) {
            boolean enabled = (Boolean) newValue;
            if (externalControlManager != null && 
                externalControlManager.getProximitySensorManager() != null) {
                externalControlManager.getProximitySensorManager().setEnabled(enabled);
            }
            return true;
        }
        
        if (key.equals(PreferenceNames.HEADSET_BUTTONS_ENABLED)) {
            boolean enabled = (Boolean) newValue;
            if (externalControlManager != null &&
                externalControlManager.getHeadsetButtonManager() != null) {

                if (enabled) {
                    // Check if we have Bluetooth permissions before enabling
                    if (!ExternalControlPermissionManager.hasBluetoothPermissions(getContext())) {
                        LOG.warn("Missing Bluetooth permissions for headset buttons");

                        // Show permission explanation dialog
                        new AlertDialog.Builder(getContext())
                            .setTitle("需要蓝牙权限")
                            .setMessage("耳机按键功能需要蓝牙权限才能正常工作。\n\n" +
                                      "权限用途：\n" +
                                      "• 检测蓝牙耳机连接状态\n" +
                                      "• 接收耳机按键事件\n\n" +
                                      "是否现在授予权限？")
                            .setPositiveButton("授予权限", (dialog, which) -> {
                                ExternalControlPermissionManager.requestBluetoothPermissions(getActivity());
                            })
                            .setNegativeButton("取消", null)
                            .show();

                        return false; // Prevent the preference from being changed
                    }
                }

                HeadsetButtonManager headsetManager = externalControlManager.getHeadsetButtonManager();

                // Try to enable/disable
                headsetManager.setEnabled(enabled);

                // Check if it actually worked
                if (enabled && !headsetManager.isEnabled()) {
                    LOG.warn("Headset button monitoring failed to enable");
                    Toast.makeText(getContext(),
                            "耳机按键监听启动失败。可能的原因：\n" +
                            "• 缺少必要的权限\n" +
                            "• 系统不支持此功能\n" +
                            "• 蓝牙耳机连接问题\n\n" +
                            "请检查设备兼容性或尝试重新连接耳机。",
                            Toast.LENGTH_LONG).show();
                    return false; // Prevent the preference from being changed
                }
            }
            return true;
        }
        
        if (key.equals(PreferenceNames.EXTERNAL_KEYBOARD_ENABLED)) {
            boolean enabled = (Boolean) newValue;
            if (externalControlManager != null &&
                externalControlManager.getExternalKeyboardManager() != null) {
                externalControlManager.getExternalKeyboardManager().setEnabled(enabled);
            }
            return true;
        }

        if (key.equals(PreferenceNames.STEERING_WHEEL_ENABLED)) {
            boolean enabled = (Boolean) newValue;
            if (externalControlManager != null &&
                externalControlManager.getSteeringWheelButtonManager() != null) {
                try {
                    externalControlManager.getSteeringWheelButtonManager().setEnabled(enabled);
                    LOG.info("Steering wheel button monitoring {}", enabled ? "enabled" : "disabled");
                } catch (Exception e) {
                    LOG.error("Failed to set steering wheel button monitoring", e);
                    Toast.makeText(getContext(),
                            "启用方向盘按键失败: " + e.getMessage(),
                            Toast.LENGTH_LONG).show();
                    return false; // Prevent the preference from being changed
                }
            }
            return true;
        }

        if (key.equals(PreferenceNames.KEYBOARD_DETECTION_MODE)) {
            String modeStr = (String) newValue;
            if (externalControlManager != null &&
                externalControlManager.getExternalKeyboardManager() != null) {
                DeviceDetectionInterface.DetectionMode mode = DeviceDetectionInterface.DetectionMode.fromString(modeStr);
                externalControlManager.getExternalKeyboardManager().setDetectionMode(mode);
                LOG.info("Keyboard detection mode changed to: {}", mode);
            }
            return true;
        }

        if (key.equals("steering_wheel_detection_mode")) {
            String modeStr = (String) newValue;
            if (externalControlManager != null &&
                externalControlManager.getSteeringWheelButtonManager() != null) {
                DeviceDetectionInterface.DetectionMode mode = DeviceDetectionInterface.DetectionMode.fromString(modeStr);
                externalControlManager.getSteeringWheelButtonManager().setDetectionMode(mode);
                LOG.info("Steering wheel detection mode changed to: {}", mode);
            }
            return true;
        }

        // Handle action list preferences
        if (preference instanceof ListPreference) {
            ListPreference listPref = (ListPreference) preference;
            updateActionPreferenceSummary(listPref, (String) newValue);
            return true;
        }
        
        return true;
    }

    @Override
    public boolean onPreferenceClick(Preference preference) {
        String key = preference.getKey();
        
        if ("keyboard_custom_keys_config".equals(key)) {
            showKeyboardMappingDialog();
            return true;
        }

        if ("keyboard_test_mode".equals(key)) {
            showKeyboardTestDialog();
            return true;
        }

        if ("keyboard_diagnostics".equals(key)) {
            showKeyboardDiagnosticsDialog();
            return true;
        }

        // Steering wheel button preferences
        if ("steering_wheel_mappings".equals(key)) {
            showSteeringWheelMappingsDialog();
            return true;
        }

        if ("steering_wheel_test".equals(key)) {
            showSteeringWheelTestDialog();
            return true;
        }

        if ("steering_wheel_status".equals(key)) {
            showSteeringWheelStatusDialog();
            return true;
        }
        
        if ("external_control_status".equals(key)) {
            updatePreferenceSummaries();
            return true;
        }
        
        if ("external_control_help".equals(key)) {
            showHelpDialog();
            return true;
        }
        
        return false;
    }

    private void showKeyboardTestDialog() {
        LOG.info("Keyboard test mode requested");

        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle("键盘事件测试");

        // Create a custom view for better display
        View testView = LayoutInflater.from(getContext()).inflate(android.R.layout.simple_list_item_2, null);
        android.widget.TextView titleText = testView.findViewById(android.R.id.text1);
        android.widget.TextView subtitleText = testView.findViewById(android.R.id.text2);

        titleText.setText("请按下外接键盘上的任意按键");
        subtitleText.setText("测试结果将在此显示\n按返回键关闭测试");

        builder.setView(testView);

        // Create a dialog that captures key events
        AlertDialog dialog = builder.create();
        dialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                if (event.getAction() == KeyEvent.ACTION_DOWN) {
                    // Log detailed information
                    LOG.info("=== 键盘测试事件 ===");
                    LOG.info("按键: {} ({})", keyCode, KeyEvent.keyCodeToString(keyCode));
                    LOG.info("设备ID: {}", event.getDeviceId());

                    // Get device information
                    InputDevice device = InputDevice.getDevice(event.getDeviceId());
                    String deviceName = "未知设备";
                    String deviceType = "未知类型";
                    boolean isExternal = false;

                    if (device != null) {
                        deviceName = device.getName();
                        deviceType = getKeyboardTypeString(device.getKeyboardType());
                        LOG.info("设备名称: '{}'", deviceName);
                        LOG.info("设备源: 0x{}", Integer.toHexString(device.getSources()));
                        LOG.info("键盘类型: {}", device.getKeyboardType());
                    }

                    // Test detection methods
                    String detectionResult = "未知";
                    String mappedAction = "无";

                    if (externalControlManager != null &&
                        externalControlManager.getExternalKeyboardManager() != null) {
                        ExternalKeyboardManager keyboardManager = externalControlManager.getExternalKeyboardManager();

                        DeviceDetectionInterface.DetectionMode mode = keyboardManager.getDetectionMode();
                        isExternal = keyboardManager.shouldProcessKeyboardEvent(event, mode);
                        detectionResult = isExternal ? "✅ 会被处理" : "❌ 不会被处理";

                        ButtonAction action = keyboardManager.getKeyAction(keyCode);
                        mappedAction = action != ButtonAction.NONE ? action.getDisplayName() : "无映射";

                        LOG.info("检测结果:");
                        LOG.info("- 当前检测模式: {}", mode);
                        LOG.info("- 是否会被处理: {}", isExternal);
                        LOG.info("- 映射的动作: {}", action);
                    }

                    LOG.info("=== 测试结束 ===");

                    // Update the dialog display
                    String keyName = KeyEvent.keyCodeToString(keyCode);
                    if (keyName.startsWith("KEYCODE_")) {
                        keyName = keyName.substring(8);
                    }

                    titleText.setText("测试结果 - " + keyName + " 键");
                    subtitleText.setText(String.format(
                        "设备: %s (ID: %d)\n" +
                        "类型: %s\n" +
                        "检测: %s\n" +
                        "动作: %s\n\n" +
                        "继续按键测试或按返回键关闭",
                        deviceName, event.getDeviceId(),
                        deviceType,
                        detectionResult,
                        mappedAction
                    ));

                    if (keyCode == KeyEvent.KEYCODE_BACK) {
                        dialog.dismiss();
                        return true;
                    }
                }
                return false;
            }
        });

        dialog.setButton(AlertDialog.BUTTON_NEGATIVE, "关闭", (d, which) -> d.dismiss());
        dialog.setButton(AlertDialog.BUTTON_NEUTRAL, "查看日志说明", (d, which) -> {
            showLogInstructions();
        });
        dialog.show();
    }

    private String getKeyboardTypeString(int keyboardType) {
        switch (keyboardType) {
            case InputDevice.KEYBOARD_TYPE_NONE:
                return "无键盘";
            case InputDevice.KEYBOARD_TYPE_NON_ALPHABETIC:
                return "非字母键盘";
            case InputDevice.KEYBOARD_TYPE_ALPHABETIC:
                return "字母键盘";
            default:
                return "未知类型(" + keyboardType + ")";
        }
    }

    private void showLogInstructions() {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle("查看详细日志");
        builder.setMessage("要查看详细的键盘测试日志，请使用以下命令：\n\n" +
                          "1. 连接设备到电脑\n" +
                          "2. 打开命令行工具\n" +
                          "3. 运行命令：\n" +
                          "   adb logcat | grep \"键盘测试事件\"\n\n" +
                          "或者查看所有外接键盘相关日志：\n" +
                          "   adb logcat | grep \"ExternalKeyboard\"");
        builder.setPositiveButton("确定", null);
        builder.show();
    }

    private void showKeyboardDiagnosticsDialog() {
        if (externalControlManager == null ||
            externalControlManager.getExternalKeyboardManager() == null) {
            Toast.makeText(getContext(), "外接键盘管理器未初始化", Toast.LENGTH_SHORT).show();
            return;
        }

        ExternalKeyboardManager keyboardManager = externalControlManager.getExternalKeyboardManager();
        String diagnosticInfo = keyboardManager.getDiagnosticInfoLegacy();

        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle("键盘诊断信息");
        builder.setMessage(diagnosticInfo);

        builder.setPositiveButton("确定", null);
        builder.setNeutralButton("重置统计", (d, which) -> {
            keyboardManager.resetDiagnostics();
            Toast.makeText(getContext(), "诊断统计已重置", Toast.LENGTH_SHORT).show();
        });
        builder.setNegativeButton("复制到剪贴板", (d, which) -> {
            android.content.ClipboardManager clipboard =
                (android.content.ClipboardManager) getContext().getSystemService(Context.CLIPBOARD_SERVICE);
            android.content.ClipData clip = android.content.ClipData.newPlainText("键盘诊断信息", diagnosticInfo);
            clipboard.setPrimaryClip(clip);
            Toast.makeText(getContext(), "诊断信息已复制到剪贴板", Toast.LENGTH_SHORT).show();
        });

        builder.show();
    }

    private void showDeleteConfirmationDialog(ExternalKeyboardManager keyboardManager,
                                            int keyCode, String keyName) {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle("删除映射确认");
        builder.setMessage("确定要删除 " + keyName + " 键的映射吗？\n\n" +
                          "删除后该按键将不再执行任何动作。\n" +
                          "您可以随时重新添加映射。");

        builder.setPositiveButton("删除", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                keyboardManager.deleteKeyMapping(keyCode);
                Toast.makeText(getContext(),
                             keyName + " 键的映射已完全删除",
                             Toast.LENGTH_SHORT).show();
            }
        });

        builder.setNegativeButton("取消", null);
        builder.show();
    }

    // Steering wheel button dialog methods
    private void showSteeringWheelMappingsDialog() {
        if (externalControlManager == null ||
            externalControlManager.getSteeringWheelButtonManager() == null) {
            Toast.makeText(getContext(), "方向盘按键管理器未初始化", Toast.LENGTH_SHORT).show();
            return;
        }

        try {
            Object steeringWheelManager = externalControlManager.getSteeringWheelButtonManager();

            // Get all button-gesture combinations using reflection
            Object combinations = steeringWheelManager.getClass()
                    .getMethod("getAllButtonGestureCombinations")
                    .invoke(steeringWheelManager);

            @SuppressWarnings("unchecked")
            java.util.List<Object> combinationsList = (java.util.List<Object>) combinations;

            // Get current mappings
            Object mappingsObj = steeringWheelManager.getClass()
                    .getMethod("getAllMappings")
                    .invoke(steeringWheelManager);

            @SuppressWarnings("unchecked")
            java.util.Map<Object, Object> currentMappings = (java.util.Map<Object, Object>) mappingsObj;

            AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
            builder.setTitle("方向盘按键映射");

            // Create display list
            java.util.List<String> displayItems = new java.util.ArrayList<>();
            java.util.List<Object> combinationObjects = new java.util.ArrayList<>();

            // Add current mappings first
            for (java.util.Map.Entry<Object, Object> entry : currentMappings.entrySet()) {
                Object combination = entry.getKey();
                Object action = entry.getValue();

                String displayName = (String) combination.getClass()
                        .getMethod("getDisplayName")
                        .invoke(combination);
                String actionName = (String) action.getClass()
                        .getMethod("getDisplayName")
                        .invoke(action);

                displayItems.add(displayName + " → " + actionName);
                combinationObjects.add(combination);
            }

            // Add separator if there are existing mappings
            if (!displayItems.isEmpty()) {
                displayItems.add("─────────────────");
                combinationObjects.add(null); // Separator marker
            }

            // Add "添加新映射" option
            displayItems.add("+ 添加新映射");
            combinationObjects.add("ADD_NEW");

            // Add "清除所有映射" option if there are existing mappings
            if (currentMappings.size() > 0) {
                displayItems.add("✗ 清除所有映射");
                combinationObjects.add("CLEAR_ALL");
            }

            String[] items = displayItems.toArray(new String[0]);

            builder.setItems(items, (dialog, which) -> {
                Object selectedItem = combinationObjects.get(which);

                if (selectedItem == null) {
                    // Separator clicked, do nothing
                    return;
                } else if ("ADD_NEW".equals(selectedItem)) {
                    showSteeringWheelAddMappingDialog();
                } else if ("CLEAR_ALL".equals(selectedItem)) {
                    showSteeringWheelClearMappingsDialog();
                } else {
                    // Existing mapping clicked, show options
                    showSteeringWheelMappingOptionsDialog(selectedItem);
                }
            });

            builder.setNegativeButton("取消", null);
            builder.show();

        } catch (Exception e) {
            LOG.error("Failed to show steering wheel mappings dialog", e);
            Toast.makeText(getContext(), "显示映射对话框失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void showSteeringWheelTestDialog() {
        if (externalControlManager == null ||
            externalControlManager.getSteeringWheelButtonManager() == null) {
            Toast.makeText(getContext(), "方向盘按键管理器未初始化", Toast.LENGTH_SHORT).show();
            return;
        }

        try {
            SteeringWheelButtonManager steeringWheelManager = externalControlManager.getSteeringWheelButtonManager();

            // Get status summary using unified interface
            String statusInfo = steeringWheelManager.getStatusSummary();

            AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
            builder.setTitle("方向盘按键测试");
            builder.setMessage(statusInfo + "\n\n" +
                              "测试方式:\n" +
                              "1. 实际按键测试：按下方向盘上的按键\n" +
                              "2. 模拟测试：点击下方按钮选择要测试的按键\n" +
                              "3. 功能测试：测试管理器基础功能\n\n" +
                              "支持的按键:\n" +
                              "• 音量+/- (KEYCODE_VOLUME_UP/DOWN)\n" +
                              "• 媒体上一首/下一首 (KEYCODE_MEDIA_PREVIOUS/NEXT)");

            builder.setPositiveButton("模拟测试", (dialog, which) -> showSteeringWheelSimulateTestDialog());
            builder.setNeutralButton("功能测试", (dialog, which) -> showSteeringWheelFunctionalityTest());
            builder.setNegativeButton("关闭", null);
            builder.show();
        } catch (Exception e) {
            LOG.error("Failed to show steering wheel test dialog", e);
            Toast.makeText(getContext(), "显示测试对话框失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void showSteeringWheelSimulateTestDialog() {
        if (externalControlManager == null ||
            externalControlManager.getSteeringWheelButtonManager() == null) {
            return;
        }

        try {
            Object steeringWheelManager = externalControlManager.getSteeringWheelButtonManager();

            // Get all button-gesture combinations
            Object combinations = steeringWheelManager.getClass()
                    .getMethod("getAllButtonGestureCombinations")
                    .invoke(steeringWheelManager);

            @SuppressWarnings("unchecked")
            java.util.List<Object> combinationsList = (java.util.List<Object>) combinations;

            AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
            builder.setTitle("选择要测试的按键和手势");

            // Create display list
            String[] items = new String[combinationsList.size()];
            for (int i = 0; i < combinationsList.size(); i++) {
                Object combination = combinationsList.get(i);
                items[i] = (String) combination.getClass()
                        .getMethod("getDisplayName")
                        .invoke(combination);
            }

            builder.setItems(items, (dialog, which) -> {
                try {
                    Object selectedCombination = combinationsList.get(which);

                    // Get button and gesture from combination
                    Object button = selectedCombination.getClass().getField("button").get(selectedCombination);
                    Object gesture = selectedCombination.getClass().getField("gesture").get(selectedCombination);

                    // Test the button press
                    boolean result = (Boolean) steeringWheelManager.getClass()
                            .getMethod("testButtonPress", button.getClass(), gesture.getClass())
                            .invoke(steeringWheelManager, button, gesture);

                    String message = result ? "测试成功执行！" : "测试执行失败，请检查映射配置。";
                    Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();

                } catch (Exception e) {
                    LOG.error("Failed to test steering wheel button", e);
                    Toast.makeText(getContext(), "测试失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                }
            });

            builder.setNegativeButton("取消", null);
            builder.show();
        } catch (Exception e) {
            LOG.error("Failed to show steering wheel simulate test dialog", e);
            Toast.makeText(getContext(), "显示模拟测试对话框失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void showSteeringWheelAddMappingDialog() {
        if (externalControlManager == null ||
            externalControlManager.getSteeringWheelButtonManager() == null) {
            return;
        }

        try {
            Object steeringWheelManager = externalControlManager.getSteeringWheelButtonManager();

            // Get all button-gesture combinations
            Object combinations = steeringWheelManager.getClass()
                    .getMethod("getAllButtonGestureCombinations")
                    .invoke(steeringWheelManager);

            @SuppressWarnings("unchecked")
            java.util.List<Object> combinationsList = (java.util.List<Object>) combinations;

            AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
            builder.setTitle("选择按键和手势");

            // Create display list
            String[] items = new String[combinationsList.size()];
            for (int i = 0; i < combinationsList.size(); i++) {
                Object combination = combinationsList.get(i);
                items[i] = (String) combination.getClass()
                        .getMethod("getDisplayName")
                        .invoke(combination);
            }

            builder.setItems(items, (dialog, which) -> {
                Object selectedCombination = combinationsList.get(which);
                showSteeringWheelActionSelectionDialog(selectedCombination);
            });

            builder.setNegativeButton("取消", null);
            builder.show();

        } catch (Exception e) {
            LOG.error("Failed to show add mapping dialog", e);
            Toast.makeText(getContext(), "显示添加映射对话框失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void showSteeringWheelMappingOptionsDialog(Object combination) {
        try {
            String displayName = (String) combination.getClass()
                    .getMethod("getDisplayName")
                    .invoke(combination);

            AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
            builder.setTitle(displayName + " 选项");

            String[] options = {"修改动作", "删除映射"};
            builder.setItems(options, (dialog, which) -> {
                if (which == 0) {
                    // 修改动作
                    showSteeringWheelActionSelectionDialog(combination);
                } else {
                    // 删除映射
                    showSteeringWheelDeleteMappingDialog(combination);
                }
            });

            builder.setNegativeButton("取消", null);
            builder.show();

        } catch (Exception e) {
            LOG.error("Failed to show mapping options dialog", e);
            Toast.makeText(getContext(), "显示映射选项失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void showSteeringWheelClearMappingsDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle("清除所有映射");
        builder.setMessage("确定要清除所有方向盘按键映射吗？\n\n此操作不可撤销。");

        builder.setPositiveButton("清除", (dialog, which) -> {
            try {
                Object steeringWheelManager = externalControlManager.getSteeringWheelButtonManager();
                steeringWheelManager.getClass()
                        .getMethod("clearAllMappings")
                        .invoke(steeringWheelManager);

                Toast.makeText(getContext(), "所有方向盘按键映射已清除", Toast.LENGTH_SHORT).show();

            } catch (Exception e) {
                LOG.error("Failed to clear all mappings", e);
                Toast.makeText(getContext(), "清除映射失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });

        builder.setNegativeButton("取消", null);
        builder.show();
    }

    private void showSteeringWheelDeleteMappingDialog(Object combination) {
        try {
            String displayName = (String) combination.getClass()
                    .getMethod("getDisplayName")
                    .invoke(combination);

            AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
            builder.setTitle("删除映射");
            builder.setMessage("确定要删除 " + displayName + " 的映射吗？");

            builder.setPositiveButton("删除", (dialog, which) -> {
                try {
                    Object steeringWheelManager = externalControlManager.getSteeringWheelButtonManager();

                    // Get button and gesture from combination
                    Object button = combination.getClass().getField("button").get(combination);
                    Object gesture = combination.getClass().getField("gesture").get(combination);

                    // Get NONE action
                    Class<?> buttonActionClass = Class.forName("com.mendhak.gpslogger.common.events.ExternalControlEvents$ButtonAction");
                    Object noneAction = buttonActionClass.getField("NONE").get(null);

                    // Set mapping to NONE
                    steeringWheelManager.getClass()
                            .getMethod("setButtonAction", button.getClass(), gesture.getClass(), buttonActionClass)
                            .invoke(steeringWheelManager, button, gesture, noneAction);

                    Toast.makeText(getContext(), displayName + " 的映射已删除", Toast.LENGTH_SHORT).show();

                } catch (Exception e) {
                    LOG.error("Failed to delete mapping", e);
                    Toast.makeText(getContext(), "删除映射失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                }
            });

            builder.setNegativeButton("取消", null);
            builder.show();

        } catch (Exception e) {
            LOG.error("Failed to show delete mapping dialog", e);
            Toast.makeText(getContext(), "显示删除对话框失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void showSteeringWheelActionSelectionDialog(Object combination) {
        try {
            String displayName = (String) combination.getClass()
                    .getMethod("getDisplayName")
                    .invoke(combination);

            AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
            builder.setTitle("选择 " + displayName + " 的动作");

            // Get allowed actions (same as keyboard mapping)
            java.util.List<ButtonAction> allowedActions = new java.util.ArrayList<>();
            allowedActions.add(ButtonAction.NONE);                // 无动作
            allowedActions.add(ButtonAction.START_STOP_LOGGING);  // 开始/停止记录
            allowedActions.add(ButtonAction.START_LOGGING);       // 开始记录
            allowedActions.add(ButtonAction.STOP_LOGGING);        // 停止记录
            allowedActions.add(ButtonAction.LOG_SINGLE_POINT);    // 记录单点
            allowedActions.add(ButtonAction.VOICE_INPUT);         // 语音输入
            allowedActions.add(ButtonAction.TEXT_INPUT);          // 文本输入
            allowedActions.add(ButtonAction.ADD_WAYPOINT);        // 添加航点
            allowedActions.add(ButtonAction.TOGGLE_ANNOTATION);   // 切换标注模式
            allowedActions.add(ButtonAction.QUICK_VOICE_INPUT);   // 一键语音输入
            allowedActions.add(ButtonAction.QUICK_TEXT_INPUT);    // 一键文本输入

            // Get annotation buttons with their actual text
            java.util.List<AnnotationButtonInfo> annotationButtons = getAnnotationButtonsInfo();
            for (AnnotationButtonInfo buttonInfo : annotationButtons) {
                ButtonAction action = ButtonAction.fromString(buttonInfo.actionName);
                allowedActions.add(action);
            }

            String[] actionNames = new String[allowedActions.size()];
            for (int i = 0; i < allowedActions.size(); i++) {
                ButtonAction action = allowedActions.get(i);
                if (action.isAnnotationButton()) {
                    // Find the corresponding annotation button info for display name
                    for (AnnotationButtonInfo buttonInfo : annotationButtons) {
                        if (buttonInfo.actionName.equals(action.name())) {
                            actionNames[i] = buttonInfo.displayName;
                            break;
                        }
                    }
                    if (actionNames[i] == null) {
                        actionNames[i] = action.getDisplayName();
                    }
                } else {
                    actionNames[i] = action.getDisplayName();
                }
            }

            builder.setItems(actionNames, (dialog, which) -> {
                try {
                    ButtonAction selectedAction = allowedActions.get(which);
                    Object steeringWheelManager = externalControlManager.getSteeringWheelButtonManager();

                    // Get button and gesture from combination
                    Object button = combination.getClass().getField("button").get(combination);
                    Object gesture = combination.getClass().getField("gesture").get(combination);

                    // Set the mapping
                    steeringWheelManager.getClass()
                            .getMethod("setButtonAction", button.getClass(), gesture.getClass(), ButtonAction.class)
                            .invoke(steeringWheelManager, button, gesture, selectedAction);

                    String message;
                    if (selectedAction == ButtonAction.NONE) {
                        message = displayName + " 映射已清除";
                    } else {
                        message = displayName + " 已映射到: " + actionNames[which];
                    }
                    Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();

                    LOG.info("Set steering wheel mapping: {} -> {}", displayName, selectedAction);

                } catch (Exception e) {
                    LOG.error("Failed to set steering wheel mapping", e);
                    Toast.makeText(getContext(), "设置映射失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                }
            });

            builder.setNegativeButton("取消", null);
            builder.show();

        } catch (Exception e) {
            LOG.error("Failed to show action selection dialog", e);
            Toast.makeText(getContext(), "显示动作选择对话框失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void showSteeringWheelStatusDialog() {
        if (externalControlManager == null ||
            externalControlManager.getSteeringWheelButtonManager() == null) {
            Toast.makeText(getContext(), "方向盘按键管理器未初始化", Toast.LENGTH_SHORT).show();
            return;
        }

        try {
            // Get status using reflection to avoid direct dependency
            Object steeringWheelManager = externalControlManager.getSteeringWheelButtonManager();
            String statusInfo = (String) steeringWheelManager.getClass()
                    .getMethod("getStatusSummary")
                    .invoke(steeringWheelManager);

            AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
            builder.setTitle("方向盘连接状态");
            builder.setMessage(statusInfo);
            builder.setPositiveButton("确定", null);
            builder.show();

        } catch (Exception e) {
            LOG.error("Failed to get steering wheel status", e);
            Toast.makeText(getContext(), "获取状态失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void showKeyboardMappingDialog() {
        LOG.info("Keyboard mapping configuration requested");

        if (externalControlManager == null ||
            externalControlManager.getExternalKeyboardManager() == null) {
            Toast.makeText(getContext(), "外接键盘管理器未初始化", Toast.LENGTH_SHORT).show();
            return;
        }

        ExternalKeyboardManager keyboardManager = externalControlManager.getExternalKeyboardManager();

        // Create dialog
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle("自定义按键映射");

        // Create list of current mappings (show all mappings but distinguish custom ones)
        List<String> mappingsList = new ArrayList<>();
        List<Integer> keyCodesList = new ArrayList<>();
        List<Boolean> isCustomList = new ArrayList<>();

        Map<Integer, ButtonAction> allMappings = keyboardManager.getAllMappings();
        Map<Integer, ButtonAction> customMappings = keyboardManager.getCustomMappings();
        Map<Integer, ButtonAction> defaultMappings = keyboardManager.getDefaultMappings();

        for (Map.Entry<Integer, ButtonAction> entry : allMappings.entrySet()) {
            int keyCode = entry.getKey();
            ButtonAction action = entry.getValue();

            // 跳过被删除的映射（设置为NONE的映射）
            if (action == ButtonAction.NONE) {
                continue;
            }

            String keyName = ExternalKeyboardManager.getKeyName(keyCode);
            String actionName = action.getDisplayName();
            boolean isCustom = customMappings.containsKey(keyCode);

            String displayText = keyName + " → " + actionName;
            if (isCustom) {
                displayText += " (自定义)";
            } else {
                displayText += " (默认)";
            }

            mappingsList.add(displayText);
            keyCodesList.add(keyCode);
            isCustomList.add(isCustom);
        }

        if (mappingsList.isEmpty()) {
            mappingsList.add("暂无按键映射");
        }

        ArrayAdapter<String> adapter = new ArrayAdapter<>(getContext(),
                android.R.layout.simple_list_item_1, mappingsList);

        builder.setAdapter(adapter, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (mappingsList.get(which).equals("暂无按键映射")) {
                    showAddKeyMappingDialog(keyboardManager);
                } else {
                    // Show options for existing mapping
                    int keyCode = keyCodesList.get(which);
                    boolean isCustom = isCustomList.get(which);
                    showKeyMappingOptionsDialog(keyboardManager, keyCode, isCustom);
                }
            }
        });

        builder.setPositiveButton("添加新映射", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                showAddKeyMappingDialog(keyboardManager);
            }
        });

        builder.setNegativeButton("关闭", null);
        builder.show();
    }

    private void showAddKeyMappingDialog(ExternalKeyboardManager keyboardManager) {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle("添加按键映射");
        builder.setMessage("请按下要映射的键盘按键...");

        // Create a simple dialog that captures key events
        AlertDialog dialog = builder.create();
        dialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                if (event.getAction() == KeyEvent.ACTION_DOWN &&
                    keyCode != KeyEvent.KEYCODE_BACK) {
                    dialog.dismiss();
                    showActionSelectionDialog(keyboardManager, keyCode);
                    return true;
                }
                return false;
            }
        });

        dialog.show();
    }

    private void showActionSelectionDialog(ExternalKeyboardManager keyboardManager, int keyCode) {
        String keyName = ExternalKeyboardManager.getKeyName(keyCode);

        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle("选择 " + keyName + " 键的动作");

        // Get allowed actions (same as volume key actions)
        List<ButtonAction> allowedActions = new ArrayList<>();
        allowedActions.add(ButtonAction.NONE);                // 无动作
        allowedActions.add(ButtonAction.START_STOP_LOGGING);  // 开始/停止记录
        allowedActions.add(ButtonAction.START_LOGGING);       // 开始记录
        allowedActions.add(ButtonAction.STOP_LOGGING);        // 停止记录
        allowedActions.add(ButtonAction.LOG_SINGLE_POINT);    // 记录单点
        allowedActions.add(ButtonAction.VOICE_INPUT);         // 语音输入
        allowedActions.add(ButtonAction.TEXT_INPUT);          // 文本输入
        allowedActions.add(ButtonAction.ADD_WAYPOINT);        // 添加航点
        allowedActions.add(ButtonAction.TOGGLE_ANNOTATION);   // 切换标注模式
        allowedActions.add(ButtonAction.QUICK_VOICE_INPUT);   // 一键语音输入
        allowedActions.add(ButtonAction.QUICK_TEXT_INPUT);    // 一键文本输入

        // Get annotation buttons with their actual text
        List<AnnotationButtonInfo> annotationButtons = getAnnotationButtonsInfo();
        for (AnnotationButtonInfo buttonInfo : annotationButtons) {
            ButtonAction action = ButtonAction.fromString(buttonInfo.actionName);
            allowedActions.add(action);
        }

        String[] actionNames = new String[allowedActions.size()];
        for (int i = 0; i < allowedActions.size(); i++) {
            ButtonAction action = allowedActions.get(i);
            if (action.isAnnotationButton()) {
                // Find the corresponding annotation button info for display name
                for (AnnotationButtonInfo buttonInfo : annotationButtons) {
                    if (buttonInfo.actionName.equals(action.name())) {
                        actionNames[i] = buttonInfo.displayName;
                        break;
                    }
                }
                if (actionNames[i] == null) {
                    actionNames[i] = action.getDisplayName();
                }
            } else {
                actionNames[i] = action.getDisplayName();
            }
        }

        builder.setItems(actionNames, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                ButtonAction selectedAction = allowedActions.get(which);
                keyboardManager.setKeyAction(keyCode, selectedAction);

                // Force reload to ensure the mapping is immediately available
                keyboardManager.reloadCustomMappings();

                String message;
                if (selectedAction == ButtonAction.NONE) {
                    message = keyName + " 键映射已清除";
                } else {
                    message = keyName + " 键已映射到: " + actionNames[which];
                }
                Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();

                LOG.info("Set key {} to action {}, current custom mappings: {}",
                        keyName, selectedAction, keyboardManager.getCustomMappings().size());
            }
        });

        builder.setNegativeButton("取消", null);
        builder.show();
    }

    private void showKeyMappingOptionsDialog(ExternalKeyboardManager keyboardManager,
                                           int keyCode, boolean isCustom) {
        String keyName = ExternalKeyboardManager.getKeyName(keyCode);
        ButtonAction currentAction = keyboardManager.getKeyAction(keyCode);

        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle(keyName + " 键选项");

        List<String> options = new ArrayList<>();
        options.add("修改动作");

        if (isCustom) {
            options.add("删除自定义映射");
        } else {
            options.add("设置自定义映射");
        }

        // 为所有映射添加完全删除选项
        options.add("删除映射");

        String[] optionsArray = options.toArray(new String[0]);

        builder.setItems(optionsArray, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (which == 0) {
                    // 修改动作
                    showActionSelectionDialog(keyboardManager, keyCode);
                } else if (which == 1) {
                    if (isCustom) {
                        // 删除自定义映射，恢复默认
                        keyboardManager.removeKeyMapping(keyCode);
                        ButtonAction defaultAction = keyboardManager.getKeyAction(keyCode);
                        String message = keyName + " 键的自定义映射已删除";
                        if (defaultAction != ButtonAction.NONE) {
                            message += "，已恢复默认动作: " + defaultAction.getDisplayName();
                        } else {
                            message += "，该键现在无动作";
                        }
                        Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
                    } else {
                        // 为默认映射设置自定义动作
                        showActionSelectionDialog(keyboardManager, keyCode);
                    }
                } else {
                    // 完全删除映射（包括默认映射）
                    showDeleteConfirmationDialog(keyboardManager, keyCode, keyName);
                }
            }
        });

        builder.setNegativeButton("取消", null);
        builder.show();
    }

    private void showHelpDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle("外部设备控制帮助");

        String helpText = "外部设备控制功能说明：\n\n" +
                "• 硬件按键：使用音量键控制GPS记录\n" +
                "• 距离传感器：通过手掌接近/远离控制\n" +
                "• 耳机按键：使用有线/无线耳机按键控制\n" +
                "• 外接键盘：使用USB/蓝牙键盘控制\n\n" +
                "可用动作：\n" +
                "• 开始/停止记录：切换GPS记录状态\n" +
                "• 记录单点：记录当前位置\n" +
                "• 一键语音输入：快速语音注释\n" +
                "• 一键文本输入：快速文本注释\n" +
                "• 注释按钮：触发自定义注释按钮";

        builder.setMessage(helpText);
        builder.setPositiveButton("确定", null);
        builder.show();
    }



    @Override
    public void onResume() {
        super.onResume();
        updatePreferenceSummaries();
        // Refresh action list preferences when returning to this fragment
        // in case annotation buttons have been modified
        refreshActionListPreferences();
    }

    /**
     * Refresh all action list preferences to update annotation button options
     */
    private void refreshActionListPreferences() {
        setupActionListPreference(PreferenceNames.VOLUME_UP_ACTION);
        setupActionListPreference(PreferenceNames.VOLUME_DOWN_ACTION);
        setupActionListPreference(PreferenceNames.PROXIMITY_NEAR_ACTION);
        setupActionListPreference(PreferenceNames.PROXIMITY_FAR_ACTION);
        setupActionListPreference(PreferenceNames.HEADSET_MEDIA_BUTTON_ACTION);
        setupActionListPreference(PreferenceNames.HEADSET_VOLUME_UP_ACTION);
        setupActionListPreference(PreferenceNames.HEADSET_VOLUME_DOWN_ACTION);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (externalControlManager != null) {
            externalControlManager.cleanup();
        }
    }

    /**
     * Get annotation buttons info with their actual text
     */
    private List<AnnotationButtonInfo> getAnnotationButtonsInfo() {
        List<AnnotationButtonInfo> buttonInfos = new ArrayList<>();

        try {
            String settings = preferenceHelper.getAnnotationButtonSettings();
            if (settings == null || settings.isEmpty()) {
                return buttonInfos;
            }

            JSONObject settingsObject = new JSONObject(settings);
            JSONArray buttonArrays = settingsObject.optJSONArray("buttons");

            if (buttonArrays != null) {
                for (int i = 0; i < buttonArrays.length(); i++) {
                    JSONObject btnObj = buttonArrays.getJSONObject(i);
                    int idx = btnObj.getInt("idx");
                    String label = btnObj.getString("label");

                    // Create display name and action name
                    String displayName = "注释按钮" + (idx + 1) + ": " + label;
                    String actionName = "ANNOTATION_BUTTON_" + (idx + 1);

                    buttonInfos.add(new AnnotationButtonInfo(displayName, actionName));
                }
            }
        } catch (Exception e) {
            LOG.warn("Error parsing annotation button settings: {}", e.getMessage());
        }

        return buttonInfos;
    }

    private void setupHeadsetGesturePreferences() {
        // Media button gestures
        setupActionListPreference(PreferenceNames.HEADSET_MEDIA_BUTTON_SINGLE_CLICK);
        setupActionListPreference(PreferenceNames.HEADSET_MEDIA_BUTTON_DOUBLE_CLICK);

        // Volume up gestures
        setupActionListPreference(PreferenceNames.HEADSET_VOLUME_UP_SINGLE_CLICK);
        setupActionListPreference(PreferenceNames.HEADSET_VOLUME_UP_DOUBLE_CLICK);

        // Volume down gestures
        setupActionListPreference(PreferenceNames.HEADSET_VOLUME_DOWN_SINGLE_CLICK);
        setupActionListPreference(PreferenceNames.HEADSET_VOLUME_DOWN_DOUBLE_CLICK);
    }

    /**
     * Helper class to hold annotation button information
     */
    private static class AnnotationButtonInfo {
        final String displayName;
        final String actionName;

        AnnotationButtonInfo(String displayName, String actionName) {
            this.displayName = displayName;
            this.actionName = actionName;
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == ExternalControlPermissionManager.REQUEST_CODE_BLUETOOTH_PERMISSIONS) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }

            if (allGranted) {
                LOG.info("Bluetooth permissions granted, enabling headset buttons");
                Toast.makeText(getContext(), "蓝牙权限已授予，正在启用耳机按键功能", Toast.LENGTH_SHORT).show();

                // Now try to enable headset buttons
                SwitchPreferenceCompat headsetPref = findPreference(PreferenceNames.HEADSET_BUTTONS_ENABLED);
                if (headsetPref != null) {
                    // Temporarily remove the listener to avoid triggering permission check again
                    headsetPref.setOnPreferenceChangeListener(null);

                    // Use a handler to delay the operation slightly to ensure permissions are fully processed
                    new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                        try {
                            // Double-check permissions are actually granted
                            if (ExternalControlPermissionManager.hasBluetoothPermissions(getContext())) {
                                LOG.info("Basic Bluetooth permissions confirmed");

                                // Check if we also need MEDIA_CONTENT_CONTROL permission
                                boolean needsMediaControl = android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU;
                                boolean hasMediaControl = ExternalControlPermissionManager.hasMediaContentControlPermission(getContext());

                                if (needsMediaControl && !hasMediaControl) {
                                    LOG.warn("MEDIA_CONTENT_CONTROL permission not granted, showing system settings guidance");
                                    headsetPref.setChecked(false);

                                    // Show dialog to guide user to system settings
                                    new AlertDialog.Builder(getContext())
                                        .setTitle("需要额外权限")
                                        .setMessage("耳机按键功能需要\"媒体内容控制\"权限。\n\n" +
                                                  "由于系统限制，此权限需要在系统设置中手动授予：\n\n" +
                                                  "1. 点击\"前往设置\"\n" +
                                                  "2. 找到\"特殊应用访问权限\"\n" +
                                                  "3. 选择\"媒体内容控制\"\n" +
                                                  "4. 找到GPSLogger并启用\n\n" +
                                                  "完成后请重新尝试启用耳机按键功能。")
                                        .setPositiveButton("前往设置", (dialog, which) -> {
                                            try {
                                                Intent intent = new Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                                                intent.setData(android.net.Uri.fromParts("package", getContext().getPackageName(), null));
                                                startActivity(intent);
                                            } catch (Exception e) {
                                                LOG.error("Failed to open app settings", e);
                                                Toast.makeText(getContext(), "无法打开设置，请手动前往应用设置", Toast.LENGTH_LONG).show();
                                            }
                                        })
                                        .setNegativeButton("取消", null)
                                        .show();
                                    return;
                                }

                                LOG.info("All permissions confirmed, proceeding with headset button enablement");

                                // Set the preference value directly in SharedPreferences
                                PreferenceHelper.getInstance().setHeadsetButtonsEnabled(true);

                                // Enable the headset button manager
                                if (externalControlManager != null && externalControlManager.getHeadsetButtonManager() != null) {
                                    HeadsetButtonManager headsetManager = externalControlManager.getHeadsetButtonManager();
                                    headsetManager.setEnabled(true);

                                    // Check if it actually worked
                                    if (headsetManager.isEnabled()) {
                                        LOG.info("Headset button monitoring successfully enabled");
                                        // Update the UI
                                        headsetPref.setChecked(true);
                                        Toast.makeText(getContext(), "耳机按键功能已成功启用", Toast.LENGTH_SHORT).show();
                                    } else {
                                        LOG.warn("Headset button monitoring failed to enable after permission grant");
                                        PreferenceHelper.getInstance().setHeadsetButtonsEnabled(false);
                                        headsetPref.setChecked(false);
                                        Toast.makeText(getContext(), "耳机按键功能启用失败，请检查设备兼容性", Toast.LENGTH_LONG).show();
                                    }
                                } else {
                                    LOG.error("ExternalControlManager or HeadsetButtonManager is null");
                                    headsetPref.setChecked(false);
                                }
                            } else {
                                LOG.warn("Basic Bluetooth permissions not granted after permission request");
                                headsetPref.setChecked(false);
                                Toast.makeText(getContext(), "蓝牙权限授予失败，请重试", Toast.LENGTH_SHORT).show();
                            }
                        } catch (Exception e) {
                            LOG.error("Error enabling headset buttons after permission grant", e);
                            headsetPref.setChecked(false);
                        } finally {
                            // Always restore the listener
                            setupHeadsetButtonsPreference();
                        }
                    }, 500); // 500ms delay to ensure permissions are processed
                }
            } else {
                LOG.warn("Bluetooth permissions denied");
                Toast.makeText(getContext(),
                    "蓝牙权限被拒绝，耳机按键功能无法使用。\n请在系统设置中手动授予权限。",
                    Toast.LENGTH_LONG).show();
            }
        }
    }

    /**
     * Setup headset buttons preference with proper listener
     */
    private void setupHeadsetButtonsPreference() {
        SwitchPreferenceCompat headsetPref = findPreference(PreferenceNames.HEADSET_BUTTONS_ENABLED);
        if (headsetPref != null) {
            headsetPref.setOnPreferenceChangeListener(this);
        }
    }

    /**
     * Show steering wheel functionality test using unified interface
     */
    private void showSteeringWheelFunctionalityTest() {
        if (externalControlManager == null ||
            externalControlManager.getSteeringWheelButtonManager() == null) {
            Toast.makeText(getContext(), "方向盘按键管理器未初始化", Toast.LENGTH_SHORT).show();
            return;
        }

        try {
            SteeringWheelButtonManager steeringWheelManager = externalControlManager.getSteeringWheelButtonManager();

            // Test functionality using unified interface
            DeviceDetectionInterface.TestResult result = steeringWheelManager.testFunctionality();

            AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
            builder.setTitle("方向盘功能测试结果");

            String message = String.format("测试状态: %s\n响应时间: %dms\n\n详细信息:\n%s",
                                          result.success ? "✅ 通过" : "❌ 失败",
                                          result.responseTime,
                                          result.message);

            builder.setMessage(message);
            builder.setPositiveButton("确定", null);
            builder.setNeutralButton("查看详细诊断", (dialog, which) -> showSteeringWheelStatusDialog());
            builder.show();

        } catch (Exception e) {
            LOG.error("Failed to test steering wheel functionality", e);
            Toast.makeText(getContext(), "功能测试失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
}
