/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components;

/**
 * Enums for annotation button layout configuration
 */
public class LayoutEnums {
    
    /**
     * Layout style for annotation buttons
     */
    public enum LayoutStyle {
        RECTANGULAR("rectangular", "矩形按钮"),
        CIRCULAR("circular", "圆形按钮");
        
        public final String key;
        public final String displayName;
        
        LayoutStyle(String key, String displayName) {
            this.key = key;
            this.displayName = displayName;
        }
        
        /**
         * Get LayoutStyle from key string
         * @param key The key string
         * @return LayoutStyle enum value, defaults to RECTANGULAR if not found
         */
        public static LayoutStyle fromKey(String key) {
            if (key == null) return RECTANGULAR;
            for (LayoutStyle style : values()) {
                if (style.key.equals(key)) return style;
            }
            return RECTANGULAR; // Default value
        }
    }
    
    /**
     * Spacing mode for annotation buttons
     */
    public enum SpacingMode {
        COMPACT("compact", "紧凑", 2),
        STANDARD("standard", "标准", 4),
        LOOSE("loose", "宽松", 8);
        
        public final String key;
        public final String displayName;
        public final int spacingDp;
        
        SpacingMode(String key, String displayName, int spacingDp) {
            this.key = key;
            this.displayName = displayName;
            this.spacingDp = spacingDp;
        }
        
        /**
         * Get SpacingMode from key string
         * @param key The key string
         * @return SpacingMode enum value, defaults to STANDARD if not found
         */
        public static SpacingMode fromKey(String key) {
            if (key == null) return STANDARD;
            for (SpacingMode mode : values()) {
                if (mode.key.equals(key)) return mode;
            }
            return STANDARD; // Default value
        }
    }
    
    /**
     * View mode for annotation buttons
     */
    public enum ViewMode {
        GRID("grid", "Grid View"),
        GROUPED("grouped", "Grouped View");

        public final String key;
        public final String displayName;

        ViewMode(String key, String displayName) {
            this.key = key;
            this.displayName = displayName;
        }

        /**
         * Get ViewMode from key string
         * @param key The key string
         * @return ViewMode enum value, defaults to GROUPED if not found
         */
        public static ViewMode fromKey(String key) {
            if (key == null) return GROUPED;
            for (ViewMode mode : values()) {
                if (mode.key.equals(key)) return mode;
            }
            return GROUPED; // Default value
        }
    }

    /**
     * Trigger mode for annotation buttons
     */
    public enum TriggerMode {

        /**
         * Voice input mode: Click button triggers voice-to-text functionality
         * Content is stored in {voice_text} variable
         */
        VOICE_INPUT("voice_input", "语音输入", "点击按钮触发语音转文字功能"),

        /**
         * Text input mode: Click button shows text input dialog
         * Content is stored in {input_text} variable
         */
        TEXT_INPUT("text_input", "文本输入", "点击按钮弹出文本输入对话框"),

        /**
         * Counter only mode: Click button only triggers counter logic
         * No input functionality, only counter increments/decrements
         */
        COUNTER_ONLY("counter_only", "计数器", "点击按钮仅触发计数器逻辑");

        private final String value;
        private final String displayName;
        private final String description;

        TriggerMode(String value, String displayName, String description) {
            this.value = value;
            this.displayName = displayName;
            this.description = description;
        }

        /**
         * Get the string value for storage
         */
        public String getValue() {
            return value;
        }

        /**
         * Get the display name for UI
         */
        public String getDisplayName() {
            return displayName;
        }

        /**
         * Get the description for UI
         */
        public String getDescription() {
            return description;
        }

        /**
         * Parse trigger mode from string value
         * @param value String value to parse
         * @return TriggerMode enum, defaults to VOICE_INPUT if not found
         */
        public static TriggerMode fromValue(String value) {
            if (value == null || value.isEmpty()) {
                return VOICE_INPUT; // Default for backward compatibility
            }

            for (TriggerMode mode : values()) {
                if (mode.value.equals(value)) {
                    return mode;
                }
            }

            return VOICE_INPUT; // Default fallback
        }

        /**
         * Get all available trigger modes for UI selection
         */
        public static TriggerMode[] getAllModes() {
            return values();
        }

        /**
         * Check if this mode requires input functionality
         */
        public boolean requiresInput() {
            return this == VOICE_INPUT || this == TEXT_INPUT;
        }

        /**
         * Check if this mode supports counter functionality
         */
        public boolean supportsCounter() {
            return true; // All modes support counter functionality
        }

        /**
         * Get the variable name associated with this trigger mode
         */
        public String getVariableName() {
            switch (this) {
                case VOICE_INPUT:
                    return "voice_text";
                case TEXT_INPUT:
                    return "input_text";
                case COUNTER_ONLY:
                    return null; // No input variable for counter-only mode
                default:
                    return null;
            }
        }

        @Override
        public String toString() {
            return displayName;
        }
    }
}
