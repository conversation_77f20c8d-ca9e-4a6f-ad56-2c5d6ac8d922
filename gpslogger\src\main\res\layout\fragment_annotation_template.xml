<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Enable/Disable Switch -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="启用模板功能"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="使用自定义模板格式化annotation内容"
                        android:textColor="@android:color/darker_gray"
                        android:textSize="14sp" />

                </LinearLayout>

                <Switch
                    android:id="@+id/enabled_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Template Editor -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="模板编辑器"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <EditText
                    android:id="@+id/template_edit_text"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginBottom="12dp"
                    android:background="@drawable/edittext_background"
                    android:gravity="top|start"
                    android:hint="输入模板内容，例如：{date} {time} - {voice_text}"
                    android:inputType="textMultiLine"
                    android:padding="12dp"
                    android:textSize="14sp" />

                <!-- Action Buttons Row 1 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/variable_selector_button"
                        style="@style/Widget.AppCompat.Button.Borderless"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="插入变量"
                        android:textColor="@color/primaryColor" />

                    <Button
                        android:id="@+id/presets_button"
                        style="@style/Widget.AppCompat.Button.Borderless"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="预设模板"
                        android:textColor="@color/primaryColor" />

                </LinearLayout>

                <!-- Action Buttons Row 2 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginTop="8dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="4dp">

                        <Button
                            android:id="@+id/config_user_variables_button"
                            style="@style/Widget.AppCompat.Button.Borderless"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="配置自定义变量"
                            android:textColor="@color/primaryColor" />

                        <Button
                            android:id="@+id/manage_counters_button"
                            style="@style/Widget.AppCompat.Button.Borderless"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="管理计数器"
                            android:textColor="@color/primaryColor" />

                    </LinearLayout>

                    <Button
                        android:id="@+id/config_numeric_counters_button"
                        style="@style/Widget.AppCompat.Button.Borderless"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="配置数值计数器"
                        android:textColor="@color/primaryColor" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Validation Status -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="语法验证"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/validation_text_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="请输入模板内容"
                    android:textSize="14sp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Preview -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="实时预览"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/preview_text_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/preview_background"
                    android:padding="12dp"
                    android:text="预览将在这里显示"
                    android:textSize="14sp"
                    android:textStyle="italic" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Save/Reset Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- First row: Save as preset -->
            <Button
                android:id="@+id/save_as_preset_button"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="保存为预设模板"
                android:textColor="@color/primaryColor" />

            <!-- Second row: Reset and Save -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/reset_button"
                    style="@style/Widget.AppCompat.Button.Borderless"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="重置"
                    android:textColor="@android:color/holo_red_dark" />

                <Button
                    android:id="@+id/save_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="保存模板"
                    android:background="@color/primaryColor"
                    android:textColor="@android:color/white" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</ScrollView>
